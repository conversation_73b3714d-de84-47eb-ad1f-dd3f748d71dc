using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Business.Services.ConstructionWeekReport;
using NSPC.Common;
using NSPC.Business.Services.ExecutionTeams;

namespace NSPC.API.V1.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/week-report")]
    [ApiExplorerSettings(GroupName = "Báo cáo tuần")]
    public class ConstructionWeekReportController : ControllerBase
    {
        private readonly IConstructionWeekReportHandler  _handlerWeekReport;

        public ConstructionWeekReportController(IConstructionWeekReportHandler handlerWeekReport)
        {
            _handlerWeekReport = handlerWeekReport;
        }
        
        /// <summary>
        /// Tạo báo cáo tuần trong dự án
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<ConstructionWeekReportViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] ConstructionWeekReportCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handlerWeekReport.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách báo cáo tuần trong dự án
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(Response<Pagination<ConstructionWeekReportViewModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilterConstructionWeekReport([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<ConstructionWeekReportQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handlerWeekReport.GetPage(filterObject);
            return Helper.TransformData(result);
        }
    }
}
