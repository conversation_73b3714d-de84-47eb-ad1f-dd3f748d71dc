using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using Serilog;
using NSPC.Data.Entity;
using static NSPC.Common.Helper;
using System.Linq.Expressions;
using NSPC.Business.Services.ConstructionActitvityLog;

namespace NSPC.Business.Services.ConstructionWeekReport
{
    public class ConstructionWeekReportHandler: IConstructionWeekReportHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        private readonly IAttachmentHandler  _attachmentHandler;
        private readonly IConstructionActivityLogHandler  _constructionActivityLogHandler;

        public ConstructionWeekReportHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper, IAttachmentHandler attachmentHandler, IConstructionActivityLogHandler constructionActivityLogHandler)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _attachmentHandler = attachmentHandler;
            _constructionActivityLogHandler = constructionActivityLogHandler;
        }

        public async Task<Response<ConstructionWeekReportViewModel>> Create(ConstructionWeekReportCreateModel model,
            RequestUser currentUser)
        {
            try
            {
                var userId =  currentUser.UserId;
                var userName = currentUser.FullName;
                
                var entity = _mapper.Map<sm_ConstructionWeekReport>(model);
                entity.Id = Guid.NewGuid();
                entity.CreatedByUserId = currentUser.UserId;
                entity.CreatedByUserName = currentUser.FullName;
                entity.CreatedOnDate = DateTime.Now;
                entity.StatusName = ConstructionConstants.FetchStatus(model.StatusCode).Name;
                entity.Code = await GetNewCode(ConstructionConstants.PrefixCode.WeekReportCode);
                _dbContext.sm_ConstructionWeekReport.Add(entity);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động thêm mới báo cáo tuần vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã thêm mới báo cáo tuần",
                        CodeLinkDescription = $"{entity.Code} - {entity.Title}",
                        OrderId =  entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion
                }
                
                // if (model.FileAttachments != null)
                // {
                //     /* Attachment Process */
                //     await processAttachment(entity.Id, model.FileAttachments);
                // }
                
                Log.Information("Thêm mới báo cáo tuần thành công, UserId: {@userId}, UserName: {@userName}, Model: {@model}", model, userId, userName);
                return Helper.CreateSuccessResponse(_mapper.Map<ConstructionWeekReportViewModel>(entity), "Thêm mới thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<ConstructionWeekReportViewModel>(ex);
            }
        }
        
        private async Task<string> GetNewCode(string defaultPrefix)
        {
            try
            {
                var code = defaultPrefix + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_ConstructionWeekReport.AsNoTracking().Where(x => x.Code.Contains(code)).OrderByDescending(x => x.CreatedOnDate).FirstOrDefaultAsync();

                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("123", ex);
                return string.Empty;
            }
        }
        
        public async Task<Response<Pagination<ConstructionWeekReportViewModel>>> GetPage(ConstructionWeekReportQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);
                var queryResult = _dbContext.sm_ConstructionWeekReport.AsNoTracking()
                    .Include(x => x.CreatedByUser)
                    .Where(predicate);
                
                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<ConstructionWeekReportViewModel>>(data);
                
                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<ConstructionWeekReportViewModel>>(ex);
            }
        }
        
        private Expression<Func<sm_ConstructionWeekReport, bool>> BuildQuery(ConstructionWeekReportQueryModel query)
        {
            var predicate = PredicateBuilder.New<sm_ConstructionWeekReport>(true);

            if (query.ConstructionId.HasValue)
            {
                predicate.And(x => x.ConstructionId == query.ConstructionId);
            }
            
            if (query.DateRange != null && query.DateRange.Count() > 0)
            {
                if (query.DateRange[0].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date >= query.DateRange[0].Value.Date);

                if (query.DateRange[1].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date < query.DateRange[1].Value.AddDays(1));
            }
            
            return predicate;
        }
        
        private async Task processAttachment(Guid weekReportId, List<jsonb_Attachment> attachments)
        {
            try
            {
                var attachmentListId = attachments.Select(x => x.Id).ToList();
                // Process attachments
                if (attachmentListId.Count > 0)
                {
                    var newWeekReport =
                        await _dbContext.sm_ConstructionWeekReport.Where(x => x.Id == weekReportId).FirstOrDefaultAsync();
                    var allAttachments = await _dbContext.erp_Attachment.Where(x => attachmentListId.Contains(x.Id))
                        .ToListAsync();

                    foreach (var att in allAttachments)
                    {
                        // UpdatePaid entity
                        att.EntityId = newWeekReport.Id;
                        att.EntityType = attachments.Where(x => x.Id == att.Id).FirstOrDefault()?.DocType;
                        att.Description = attachments.Where(x => x.Id == att.Id).FirstOrDefault()?.Description;

                        // Move files to new folder
                        var moveFileResult = _attachmentHandler.MoveEntityAttachment(att.DocType, att.EntityType,
                            newWeekReport.Id, att.FilePath, newWeekReport.CreatedOnDate);
                        if (moveFileResult.IsSuccess)
                            att.FilePath = moveFileResult.Data;
                    }

                    if (allAttachments != null && allAttachments.Count() > 0)
                        newWeekReport.FileAttachments = allAttachments.Select(x => new jsonb_Attachment
                        {
                            Description = x.Description,
                            FileName = x.OriginalFileName,
                            DocType = x.DocType,
                            FilePath = x.FilePath,
                            //Name = x.Name,
                            Id = x.Id
                        }).ToList();
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }
    }
}
