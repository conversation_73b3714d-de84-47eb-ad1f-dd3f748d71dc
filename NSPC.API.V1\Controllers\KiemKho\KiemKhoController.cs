﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.KiemKho
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/kiem-kho")]
    [ApiExplorerSettings(GroupName = "Quản lý phiên kiểm kho")]
    [Authorize]
    public class KiemKhoController : ControllerBase
    {
        private readonly IKiemKhoHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public KiemKhoController(IKiemKhoHandler handler)
        {
            _handler = handler;
        }


        /// <summary>
        /// Tạo phiên kiểm kho
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<KiemKhoDetailViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] KiemKhoCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật phiên kiểm kho
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<KiemKhoDetailViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] KiemKhoCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 phiên kiểm kho
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<KiemKhoDetailViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách phiên kiểm kho
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<KiemKhoViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<KiemKhoQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 phiên kiểm kho
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<KiemKhoDetailViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa danh sách phiên kiểm kho
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpDelete, Route("")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMany([FromQuery] List<Guid> ids)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.DeleteMany(ids);

            return Helper.TransformData(result);
        }

        /*/// <summary>
        /// Láy số lượng tồn kho của 1 vật tu
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpGet, Route("ton-kho/{maVatTu}")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetSoLuongTonKhoVatTu(string maVatTu, string maKho, int index)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.GetSoLuongTonKhoVatTu(maVatTu,maKho,index);

            return Helper.TransformData(result);
        }*/

        /// <summary>
        /// import từ excel
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("excel/import")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> Import(string path)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.Import(path);

            return Helper.TransformData(result);
        }
        /// <summary>
        /// import từ excel
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("excel/export/{id}")]
        [EnableCors("AllowOrigin")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<FileResult> Export(Guid id, string path)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.Export(id,path);
            if (result.IsSuccess)
                return File(result.Data.Data, "application/octet-stream", $"{result.Data.CheckInventoryCode}.xlsx");
            return File(new byte[] { }, "application/octet-stream", "ExelTemplate.xlsx");
        }

        /// <summary>
        /// Cân bằng kho
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("balance-inventory/{id}")]
        [ProducesResponseType(typeof(Response<KiemKhoDetailViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> BalanceInventory(Guid id, [FromBody] KiemKhoCreateUpdateModel model)
        {
            var result = await _handler.BalanceInventory(id, model);
            return Helper.TransformData(result);
        }
    }
}
