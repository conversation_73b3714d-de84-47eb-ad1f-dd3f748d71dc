using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using NSPC.Data.Data.Entity.Contract;
using Serilog;
using System.Linq.Expressions;
using NSPC.Business.Services.ConstructionActitvityLog;
using NSPC.Data.Data.Entity.ActivityHistory;

namespace NSPC.Business.Services.Contract
{
    public class ContractHandler : IContractHandler
    {
        private readonly List<ImplementationStatus> _statusList = new()
        {
            ImplementationStatus.PendingApproval,
            ImplementationStatus.NotImplemented,
            ImplementationStatus.Approved,
            ImplementationStatus.InProgress,
            ImplementationStatus.OnHoldOrSuspended,
        };

        private readonly SMDbContext _dbContext;
        private readonly IMapper _mapper;
        private readonly IAttachmentHandler _attachmentHandler;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IConstructionActivityLogHandler _constructionActivityLogHandler;

        public ContractHandler(
            SMDbContext dbContext,
            IMapper mapper,
            IAttachmentHandler attachmentHandler,
            IHttpContextAccessor httpContextAccessor,
            IConstructionActivityLogHandler constructionActivityLogHandler
        )
        {
            _dbContext = dbContext;
            _mapper = mapper;
            _attachmentHandler = attachmentHandler;
            _httpContextAccessor = httpContextAccessor;
            _constructionActivityLogHandler = constructionActivityLogHandler;
        }

        public async Task<Response<ContractViewModel>> Create(ContractCreateUpdateModel model)
        {
            try
            {
                var validationResult = await ValidateCreateUpdateModel<ContractViewModel>(model);
                if (validationResult != null)
                {
                    return validationResult;
                }

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
                var contract = new sm_Contract
                {
                    Id = Guid.NewGuid(),
                    Code = model.Code,
                    ContractNumber = model.ContractNumber,
                    ConstructionId = model.ConstructionId,
                    TemplateStageId = model.TemplateStageId,
                    AssignmentAYear = model.AssignmentAYear,
                    ConsultingServiceId = model.ConsultingServiceId,
                    ValueBeforeVatAmount = model.ValueBeforeVatAmount,
                    ExpectedVolume = model.ExpectedVolume,
                    AcceptanceValueBeforeVatAmount = model.AcceptanceValueBeforeVatAmount,
                    PaidAmount = model.PaidAmount,
                    TaxRatePercentage = model.TaxRatePercentage,
                    ContractSigningDate = model.ContractSigningDate,
                    ContractDurationDays = model.ContractDurationDays,
                    Issues = model.Issues,
                    Notes = model.Notes,
                    ImplementationStatus = Enum.Parse<ImplementationStatus>(model.ImplementationStatus),
                    AcceptanceDocumentStatus = Enum.Parse<AcceptanceDocumentStatus>(model.AcceptanceDocumentStatus),
                    ExpectedApprovalMonth = model.ExpectedApprovalMonth,
                    ApprovalDate = model.ApprovalDate,
                    DesignApprovalDate = model.DesignApprovalDate,
                    ExpectedAcceptanceMonth = model.ExpectedAcceptanceMonth,
                    InvoiceStatus = Enum.Parse<InvoiceStatus>(model.InvoiceStatus),
                    InvoiceIssuanceDates = model.InvoiceIssuanceDates,
                    AcceptanceYear = model.AcceptanceYear,
                    HandoverRecordDate = model.HandoverRecordDate,
                    SiteSurveyRecordDate = model.SiteSurveyRecordDate,
                    SurveyAcceptanceRecordDate = model.SurveyAcceptanceRecordDate,
                    SupplementaryContractRequired =
                        Enum.Parse<SupplementaryContractRequired>(model.SupplementaryContractRequired),
                    AcceptancePlan = model.AcceptancePlan,
                    CreatedOnDate = DateTime.Now,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.UserName,
                    LastModifiedOnDate = DateTime.Now,
                    LastModifiedByUserId = currentUser.UserId,
                    LastModifiedByUserName = currentUser.UserName,
                    TenantId = currentUser.TenantId
                };
                contract.Appendices = await ProcessAppendixAttachment(contract, model.Appendices);

                var activityHistory = new sm_ActiviyHisroty
                {
                    Id = Guid.NewGuid(),
                    EntityId = contract.Id,
                    EntityType = "Contract",
                    Action = ContractHistoryAction.CREATE,
                    Description = null,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.UserName,
                    CreatedOnDate = DateTime.Now,
                    LastModifiedByUserId = currentUser.UserId,
                    LastModifiedByUserName = currentUser.UserName,
                    LastModifiedOnDate = DateTime.Now,
                    TenantId = currentUser.TenantId
                };

                _dbContext.sm_Contract.Add(contract);
                _dbContext.sm_ActiviyHisroty.Add(activityHistory);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động thêm mới hợp đồng vào bảng sm_ConstructionActivityLog

                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã thêm mới hợp đồng",
                        CodeLinkDescription = $"{contract.Code}",
                        OrderId = contract.Id,
                        ConstructionId = contract.ConstructionId,
                    }, currentUser);

                    #endregion
                }

                var result = await _dbContext.sm_Contract
                    .Include(x => x.Construction)
                    .Include(x => x.TemplateStage)
                    .Include(x => x.ConsultingService)
                    .FirstOrDefaultAsync(x => x.Id == contract.Id);

                return Helper.CreateSuccessResponse(
                    _mapper.Map<ContractViewModel>(result),
                    "Tạo hợp đồng thành công"
                );
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<ContractViewModel>(e);
            }
        }

        public async Task<Response<ContractViewModel>> Update(Guid id, ContractCreateUpdateModel model)
        {
            try
            {
                var contract = await _dbContext.sm_Contract.FindAsync(id);
                if (contract == null)
                {
                    return Helper.CreateBadRequestResponse<ContractViewModel>("Không tìm thấy hợp đồng");
                }

                var validationResult = await ValidateCreateUpdateModel<ContractViewModel>(model, id);
                if (validationResult != null)
                {
                    return validationResult;
                }

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                contract.Code = model.Code;
                contract.ContractNumber = model.ContractNumber;
                contract.ConstructionId = model.ConstructionId;
                contract.TemplateStageId = model.TemplateStageId;
                contract.AssignmentAYear = model.AssignmentAYear;
                contract.ConsultingServiceId = model.ConsultingServiceId;
                contract.ValueBeforeVatAmount = model.ValueBeforeVatAmount;
                contract.ExpectedVolume = model.ExpectedVolume;
                contract.AcceptanceValueBeforeVatAmount = model.AcceptanceValueBeforeVatAmount;
                contract.PaidAmount = model.PaidAmount;
                contract.TaxRatePercentage = model.TaxRatePercentage;
                contract.ContractSigningDate = model.ContractSigningDate;
                contract.ContractDurationDays = model.ContractDurationDays;
                contract.Issues = model.Issues;
                contract.Notes = model.Notes;
                contract.ImplementationStatus = Enum.Parse<ImplementationStatus>(model.ImplementationStatus);
                contract.AcceptanceDocumentStatus =
                    Enum.Parse<AcceptanceDocumentStatus>(model.AcceptanceDocumentStatus);
                contract.ExpectedApprovalMonth = model.ExpectedApprovalMonth;
                contract.ApprovalDate = model.ApprovalDate;
                contract.DesignApprovalDate = model.DesignApprovalDate;
                contract.ExpectedAcceptanceMonth = model.ExpectedAcceptanceMonth;
                contract.InvoiceStatus = Enum.Parse<InvoiceStatus>(model.InvoiceStatus);
                contract.InvoiceIssuanceDates = model.InvoiceIssuanceDates;
                contract.AcceptanceYear = model.AcceptanceYear;
                contract.HandoverRecordDate = model.HandoverRecordDate;
                contract.SiteSurveyRecordDate = model.SiteSurveyRecordDate;
                contract.SurveyAcceptanceRecordDate = model.SurveyAcceptanceRecordDate;
                contract.SupplementaryContractRequired =
                    Enum.Parse<SupplementaryContractRequired>(model.SupplementaryContractRequired);
                contract.AcceptancePlan = model.AcceptancePlan;
                contract.LastModifiedOnDate = DateTime.Now;
                contract.LastModifiedByUserId = currentUser.UserId;
                contract.LastModifiedByUserName = currentUser.UserName;
                contract.Appendices = await ProcessAppendixAttachment(contract, model.Appendices, contract.Appendices);

                var activityHistory = new sm_ActiviyHisroty
                {
                    Id = Guid.NewGuid(),
                    EntityId = contract.Id,
                    EntityType = "Contract",
                    Action = ContractHistoryAction.UPDATE,
                    Description = null,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.UserName,
                    CreatedOnDate = DateTime.Now,
                    LastModifiedByUserId = currentUser.UserId,
                    LastModifiedByUserName = currentUser.UserName,
                    LastModifiedOnDate = DateTime.Now,
                    TenantId = currentUser.TenantId
                };

                _dbContext.sm_Contract.Update(contract);
                _dbContext.sm_ActiviyHisroty.Add(activityHistory);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động cập nhật thông tin hợp đồng vào bảng sm_ConstructionActivityLog

                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã cập nhật thông tin hợp đồng",
                        CodeLinkDescription = $"{contract.Code}",
                        OrderId = contract.Id,
                        ConstructionId = contract.ConstructionId,
                    }, currentUser);

                    #endregion
                }

                var result = await _dbContext.sm_Contract
                    .Include(x => x.Construction)
                    .Include(x => x.TemplateStage)
                    .Include(x => x.ConsultingService)
                    .FirstOrDefaultAsync(x => x.Id == contract.Id);

                return Helper.CreateSuccessResponse(
                    _mapper.Map<ContractViewModel>(result),
                    "Cập nhật hợp đồng thành công"
                );
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Id: {id}, Model: {@model}", id, model);
                return Helper.CreateExceptionResponse<ContractViewModel>(e);
            }
        }

        public async Task<Response<Pagination<ContractViewModel>>> GetPage(ContractQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);
                var queryResult = _dbContext.sm_Contract
                    .Include(x => x.Construction)
                    .ThenInclude(x => x.sm_Investor)
                    .Include(x => x.TemplateStage)
                    .Include(x => x.ConsultingService)
                    .Where(predicate);

                var data = await queryResult.GetPageAsync(query);
                var result = _mapper.Map<Pagination<ContractViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Query: {@query}", query);
                return Helper.CreateExceptionResponse<Pagination<ContractViewModel>>(e);
            }
        }

        public async Task<Response<Dictionary<string, int>>> CountByStatus(ContractQueryModel query)
        {
            var predicate = BuildQuery(query, true);
            predicate.And(x => _statusList.Contains(x.ImplementationStatus));

            var result = await _dbContext.sm_Contract
                .Where(predicate)
                .GroupBy(x => x.ImplementationStatus)
                .Select(x => new { x.Key, Count = x.Count() })
                .ToDictionaryAsync(x => x.Key.ToString(), x => x.Count);

            result["All"] = result.Values.Sum();

            return Helper.CreateSuccessResponse(result);
        }

        public async Task<Response<ContractDetailViewModel>> GetById(Guid id)
        {
            try
            {
                var contract = await _dbContext.sm_Contract
                    .AsNoTracking()
                    .Include(x => x.Construction)
                    .ThenInclude(x => x.sm_Investor)
                    .Include(x => x.TemplateStage)
                    .Include(x => x.ConsultingService)
                    .Include(x => x.CreatedByUser)
                    .Include(x => x.LastModifiedByUser)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (contract == null)
                {
                    return Helper.CreateBadRequestResponse<ContractDetailViewModel>("Không tìm thấy hợp đồng");
                }

                var activityHistories = await _dbContext.sm_ActiviyHisroty
                    .Where(x => x.EntityId == contract.Id && x.EntityType == "Contract")
                    .OrderByDescending(x => x.CreatedOnDate)
                    .ToListAsync();
                var userIds = activityHistories
                    .Select(x => x.CreatedByUserId)
                    .Concat(activityHistories.Select(x => x.LastModifiedByUserId!.Value))
                    .Distinct()
                    .ToList();
                var users = await _dbContext.IdmUser
                    .Where(x => userIds.Contains(x.Id))
                    .ToDictionaryAsync(x => x.Id, x => x.Name);
                var result = _mapper.Map<ContractDetailViewModel>(contract);

                result.ActivityHistories = _mapper.Map<List<ActivityHistoryViewModel>>(activityHistories);
                result.ActivityHistories.ForEach(x =>
                {
                    x.CreatedByUserFullName = users[x.CreatedByUserId];
                    x.LastModifiedByFullName = users[x.LastModifiedByUserId!.Value];
                });

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Id: {id}", id);
                return Helper.CreateExceptionResponse<ContractDetailViewModel>(e);
            }
        }

        public async Task<Response> Delete(Guid id)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                var contract = await _dbContext.sm_Contract.FindAsync(id);
                if (contract == null)
                {
                    return Helper.CreateBadRequestResponse("Không tìm thấy hợp đồng");
                }

                var activityHistory = new sm_ActiviyHisroty
                {
                    Id = Guid.NewGuid(),
                    EntityId = contract.Id,
                    EntityType = "Contract",
                    Action = ContractHistoryAction.DELETE,
                    Description = null,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.UserName,
                    CreatedOnDate = DateTime.Now,
                    LastModifiedByUserId = currentUser.UserId,
                    LastModifiedByUserName = currentUser.UserName,
                    LastModifiedOnDate = DateTime.Now,
                    TenantId = currentUser.TenantId
                };

                _dbContext.sm_Contract.Remove(contract);
                _dbContext.sm_ActiviyHisroty.Add(activityHistory);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động xoá hợp đồng vào bảng sm_ConstructionActivityLog

                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã xoá hợp đồng",
                        CodeLinkDescription = $"{contract.Code}",
                        OrderId = contract.Id,
                        ConstructionId = contract.ConstructionId,
                    }, currentUser);

                    #endregion
                }

                return Helper.CreateSuccessResponse("Xóa hợp đồng thành công");
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Id: {id}", id);
                return Helper.CreateExceptionResponse(e);
            }
        }

        private async Task<List<ContractAppendixItem>> ProcessAppendixAttachment(
            sm_Contract contract,
            List<ContractAppendixInputItem> inputAppendices,
            List<ContractAppendixItem> oldAppendices = null
        )
        {
            try
            {
                oldAppendices ??= new List<ContractAppendixItem>();

                var oldAppendixAttachmentIdList = oldAppendices
                    .Where(x => x.Attachment != null)
                    .Select(x => x.Attachment.Id)
                    .ToList();
                var attachmentIdList = inputAppendices
                    .Where(x => x.AttachmentId.HasValue && !oldAppendixAttachmentIdList.Contains(x.AttachmentId.Value))
                    .Select(x => x.AttachmentId.Value)
                    .ToList();
                var allAttachments = attachmentIdList.Count == 0
                    ? new List<erp_Attachment>()
                    : await _dbContext.erp_Attachment
                        .Where(x => attachmentIdList.Contains(x.Id) && !oldAppendixAttachmentIdList.Contains(x.Id))
                        .ToListAsync();

                var result = new List<ContractAppendixItem>();

                foreach (var appendixInputItem in inputAppendices)
                {
                    var appendixItem = new ContractAppendixItem
                    {
                        Content = appendixInputItem.Content
                    };

                    result.Add(appendixItem);

                    if (!appendixInputItem.AttachmentId.HasValue) continue;

                    var matchOldAppendix = oldAppendices
                        .Where(x => x.Attachment != null)
                        .Select(x => x.Attachment)
                        .FirstOrDefault(x => x.Id == appendixInputItem.AttachmentId);

                    if (matchOldAppendix != null)
                    {
                        appendixItem.Attachment = matchOldAppendix;
                        continue;
                    }

                    var matchAttachment = allAttachments.FirstOrDefault(x => x.Id == appendixInputItem.AttachmentId);
                    if (matchAttachment == null) continue;

                    var moveFileResult = _attachmentHandler.MoveEntityAttachment(matchAttachment.DocType,
                        matchAttachment.EntityType,
                        contract.Id, matchAttachment.FilePath, contract.CreatedOnDate);
                    if (!moveFileResult.IsSuccess) continue;

                    matchAttachment.EntityId = contract.Id;
                    matchAttachment.FilePath = moveFileResult.Data;
                    appendixItem.Attachment = new AppendixAttachment
                    {
                        Id = matchAttachment.Id,
                        FileName = matchAttachment.OriginalFileName,
                        FileType = matchAttachment.FileType,
                        FilePath = moveFileResult.Data
                    };
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        private Expression<Func<sm_Contract, bool>> BuildQuery(ContractQueryModel query, bool queryForCount = false)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
            var predicate = PredicateBuilder.New<sm_Contract>(true);

            if (currentUser.TenantId != null)
            {
                predicate.And(x => x.TenantId == currentUser.TenantId);
            }

            // FullTextSearch tìm kiếm theo Code và tên của Construction
            if (!string.IsNullOrWhiteSpace(query.FullTextSearch))
            {
                predicate.And(x =>
                    x.Code.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                    x.Construction.Name.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                    x.AssignmentAYear.ToString() == query.FullTextSearch.Trim());
            }

            // Tìm kiếm theo Code
            if (!string.IsNullOrWhiteSpace(query.Code))
            {
                predicate.And(x => x.Code.ToLower().Contains(query.Code.Trim().ToLower()));
            }

            // Tìm kiếm theo ContractNumber
            if (!string.IsNullOrWhiteSpace(query.ContractNumber))
            {
                predicate.And(x => x.ContractNumber.ToLower().Contains(query.ContractNumber.Trim().ToLower()));
            }

            // Tìm kiếm theo ConstructionId
            if (query.ConstructionId.HasValue && query.ConstructionId.Value != Guid.Empty)
            {
                predicate.And(x => x.ConstructionId == query.ConstructionId.Value);
            }

            // Tìm kiếm theo TemplateStageId
            if (query.TemplateStageId.HasValue && query.TemplateStageId.Value != Guid.Empty)
            {
                predicate.And(x => x.TemplateStageId == query.TemplateStageId.Value);
            }

            // Tìm kiếm theo ConsultingServiceId
            if (query.ConsultingServiceId.HasValue && query.ConsultingServiceId.Value != Guid.Empty)
            {
                predicate.And(x => x.ConsultingServiceId == query.ConsultingServiceId.Value);
            }

            // Tìm kiếm theo AssignmentAYear
            if (query.AssignmentAYear.HasValue)
            {
                predicate.And(x => x.AssignmentAYear == query.AssignmentAYear.Value);
            }

            // Tìm kiếm theo khoảng giá trị hợp đồng (trước VAT)
            if (query.ValueBeforeVatAmountRange != null && query.ValueBeforeVatAmountRange.Length > 0)
            {
                if (query.ValueBeforeVatAmountRange[0].HasValue)
                {
                    predicate.And(x => x.ValueBeforeVatAmount >= query.ValueBeforeVatAmountRange[0].Value);
                }

                if (query.ValueBeforeVatAmountRange.Length > 1 && query.ValueBeforeVatAmountRange[1].HasValue)
                {
                    predicate.And(x => x.ValueBeforeVatAmount <= query.ValueBeforeVatAmountRange[1].Value);
                }
            }

            // Tìm kiếm theo khoảng thời gian phê duyệt
            if (query.ApprovalDateRange != null && query.ApprovalDateRange.Length > 0)
            {
                if (query.ApprovalDateRange[0].HasValue)
                {
                    predicate.And(x => x.ApprovalDate >= query.ApprovalDateRange[0].Value.Date);
                }

                if (query.ApprovalDateRange.Length > 1 && query.ApprovalDateRange[1].HasValue)
                {
                    predicate.And(x => x.ApprovalDate <= query.ApprovalDateRange[1].Value.Date.AddDays(1).AddTicks(-1));
                }
            }

            // Tìm kiếm theo khoảng thời gian xuất hóa đơn
            if (query.InvoiceIssuanceDateRange != null && query.InvoiceIssuanceDateRange.Length > 0)
            {
                if (query.InvoiceIssuanceDateRange[0].HasValue)
                {
                    predicate.And(x =>
                        x.InvoiceIssuanceDates.Any(date => date >= query.InvoiceIssuanceDateRange[0].Value.Date));
                }

                if (query.InvoiceIssuanceDateRange.Length > 1 && query.InvoiceIssuanceDateRange[1].HasValue)
                {
                    predicate.And(x => x.InvoiceIssuanceDates.Any(date =>
                        date <= query.InvoiceIssuanceDateRange[1].Value.Date.AddDays(1).AddTicks(-1)));
                }
            }

            if (!queryForCount)
            {
                // Tìm kiếm theo tình hình thực hiện
                if (!string.IsNullOrWhiteSpace(query.ImplementationStatus))
                {
                    if (Enum.TryParse<ImplementationStatus>(query.ImplementationStatus, out var implementationStatus))
                    {
                        predicate.And(x => x.ImplementationStatus == implementationStatus);
                    }
                    else
                    {
                        predicate.And(x => false);
                    }
                }

                // Tìm kiếm theo tình hình lập hồ sơ nghiệm thu
                if (!string.IsNullOrWhiteSpace(query.AcceptanceDocumentStatus))
                {
                    if (Enum.TryParse<AcceptanceDocumentStatus>(query.AcceptanceDocumentStatus,
                            out var acceptanceDocumentStatus))
                    {
                        predicate.And(x => x.AcceptanceDocumentStatus == acceptanceDocumentStatus);
                    }
                    else
                    {
                        predicate.And(x => false);
                    }
                }

                // Tìm kiếm theo tình hình xuất hoá đơn
                if (!string.IsNullOrWhiteSpace(query.InvoiceStatus))
                {
                    if (Enum.TryParse<InvoiceStatus>(query.InvoiceStatus, out var invoiceStatus))
                    {
                        predicate.And(x => x.InvoiceStatus == invoiceStatus);
                    }
                    else
                    {
                        predicate.And(x => false);
                    }
                }

                // Tìm kiếm theo cần ký PLHĐ
                if (!string.IsNullOrWhiteSpace(query.SupplementaryContractRequired))
                {
                    if (Enum.TryParse<SupplementaryContractRequired>(query.SupplementaryContractRequired,
                            out var supplementaryContractRequired))
                    {
                        predicate.And(x => x.SupplementaryContractRequired == supplementaryContractRequired);
                    }
                    else
                    {
                        predicate.And(x => false);
                    }
                }
            }

            // Tìm kiếm theo cấp điện áp
            if (!string.IsNullOrWhiteSpace(query.VoltageTypeCode))
            {
                predicate.And(x =>
                    x.Construction.VoltageTypeCode.ToLower().Contains(query.VoltageTypeCode.Trim().ToLower()));
            }

            return predicate;
        }

        private async Task<Response<T>> ValidateCreateUpdateModel<T>(ContractCreateUpdateModel model, Guid? id = null)
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(model.Code))
            {
                return Helper.CreateBadRequestResponse<T>("Mã hợp đồng không được để trống");
            }

            if (model.ConstructionId == Guid.Empty)
            {
                return Helper.CreateBadRequestResponse<T>("Công trình/dự án không được để trống");
            }

            if (model.TemplateStageId == Guid.Empty)
            {
                return Helper.CreateBadRequestResponse<T>("Giai đoạn không được để trống");
            }

            if (model.ConsultingServiceId == Guid.Empty)
            {
                return Helper.CreateBadRequestResponse<T>("Dịch vụ tư vấn không được để trống");
            }

            if (model.AssignmentAYear <= 0)
            {
                return Helper.CreateBadRequestResponse<T>("Năm giao A phải lớn hơn 0");
            }

            if (!Enum.TryParse<ImplementationStatus>(model.ImplementationStatus, out _))
            {
                return Helper.CreateBadRequestResponse<T>("Tình hình thực hiện không hợp lệ");
            }

            if (!Enum.TryParse<AcceptanceDocumentStatus>(model.AcceptanceDocumentStatus, out _))
            {
                return Helper.CreateBadRequestResponse<T>("Tình hình lập hồ sơ nghiệm thu không hợp lệ");
            }

            if (!Enum.TryParse<InvoiceStatus>(model.InvoiceStatus, out _))
            {
                return Helper.CreateBadRequestResponse<T>("Tình hình xuất hoá đơn không hợp lệ");
            }

            if (!Enum.TryParse<SupplementaryContractRequired>(model.SupplementaryContractRequired, out _))
            {
                return Helper.CreateBadRequestResponse<T>("Cần ký PLHĐ không hợp lệ");
            }

            // Check if Code is unique
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
            var exists = await _dbContext.sm_Contract
                .AnyAsync(x =>
                    x.Code.ToLower() == model.Code.ToLower() && (id == null || x.Id != id) &&
                    x.TenantId == currentUser.TenantId);

            if (exists)
            {
                return Helper.CreateBadRequestResponse<T>("Mã hợp đồng đã tồn tại");
            }

            // Validate Construction exists
            var constructionExists = await _dbContext.sm_Construction.AnyAsync(x => x.Id == model.ConstructionId);
            if (!constructionExists)
            {
                return Helper.CreateBadRequestResponse<T>("Công trình/dự án không tồn tại");
            }

            // Validate TemplateStage exists
            var templateStageExists = await _dbContext.sm_TemplateStage.AnyAsync(x => x.Id == model.TemplateStageId);
            if (!templateStageExists)
            {
                return Helper.CreateBadRequestResponse<T>("Giai đoạn không tồn tại");
            }

            // Validate ConsultingService exists and has the correct type (CodeTypeConstants.ConsultService)
            var consultingService = await _dbContext.sm_CodeType
                .FirstOrDefaultAsync(x =>
                    x.Id == model.ConsultingServiceId && x.Type == CodeTypeConstants.ConsultService);

            if (consultingService == null)
            {
                return Helper.CreateBadRequestResponse<T>("Dịch vụ tư vấn không tồn tại");
            }

            return null;
        }

        private Expression<Func<sm_Contract, bool>> BuildQueryDebtReport(DebtReportQueryModel query)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
            var predicate = PredicateBuilder.New<sm_Contract>(true);

            if (currentUser.TenantId != null)
            {
                predicate.And(x => x.TenantId == currentUser.TenantId);
            }

            if (!string.IsNullOrWhiteSpace(query.FullTextSearch))
            {
                predicate.And(x =>
                    x.Code.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                    x.Construction.Name.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                    x.AssignmentAYear.ToString() == query.FullTextSearch.Trim());
            }

            // if (query.MinClosingDebt.HasValue)
            //     predicate = predicate.And(x => x.SettlementValueAmount - x.PaidAmount >= query.MinClosingDebt.Value);

            // if (query.MaxClosingDebt.HasValue)
            //     predicate = predicate.And(x => x.SettlementValueAmount - x.PaidAmount <= query.MaxClosingDebt.Value);


            // Query Nợ cuối kỳ khác 0 (Positive)
            // if (query.Positive.HasValue && query.Positive.Value)
            //     predicate = predicate.And(x => x.SettlementValueAmount - x.PaidAmount != 0);
            return predicate;
        }

        public async Task<Response<Pagination<DebtReportViewModel>>> GetPageDebtReport(DebtReportQueryModel query)
        {
            try
            {
                var predicate = BuildQueryDebtReport(query);
                if (query.GroupBy == "project") return await GetPageDebtReportProject(query);
                if (query.GroupBy == "investor") return await GetPageDebtReportInvestor(query);
                var queryResult = _dbContext.sm_Contract
                    .Include(x => x.Construction)
                    .ThenInclude(x => x.sm_Investor)
                    .Include(x => x.TemplateStage)
                    .Include(x => x.ConsultingService)
                    .Where(predicate);

                var data = await queryResult.GetPageAsync(query);

                var result = new Pagination<DebtReportViewModel>
                {
                    Page = data.Page,
                    TotalPages = data.TotalPages,
                    Size = data.Size,
                    NumberOfElements = data.NumberOfElements,
                    TotalElements = data.TotalElements,
                    Content = data.Content.Select(contract => new DebtReportViewModel
                    {
                        Id = contract.Id,
                        Code = contract.Code,
                        Name = contract.Construction?.Name,
                        InvestorTypeName = contract.Construction?.sm_Investor?.Name,
                        // SettlementValueAmount = contract.SettlementValueAmount,
                        PaidAmount = contract.PaidAmount,
                        Construction = _mapper.Map<ConstructionViewModel>(contract.Construction),
                    }).ToList()
                };

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                Log.Information("Params: Query: {@query}", query);
                return Helper.CreateExceptionResponse<Pagination<DebtReportViewModel>>(e);
            }
        }

        public async Task<Response<Pagination<DebtReportViewModel>>> GetPageDebtReportProject(
            DebtReportQueryModel query)
        {
            try
            {
                var predicate = BuildQueryDebtReport(query);
                var queryResult = _dbContext.sm_Contract.Include(x => x.Construction);

                var groupedQuery = queryResult
                    .GroupBy(x => new { x.ConstructionId, x.Construction.Code, x.Construction.Name })
                    .Select(g => new DebtReportViewModel
                    {
                        Id = g.Key.ConstructionId,
                        Code = g.Key.Code,
                        Name = g.Key.Name,
                        // SettlementValueAmount = g.Sum(x => x.SettlementValueAmount ?? 0),
                        PaidAmount = g.Sum(x => x.PaidAmount ?? 0)
                    });
                if (query.MinClosingDebt.HasValue)
                    groupedQuery = groupedQuery.Where(x =>
                        x.SettlementValueAmount - x.PaidAmount >= query.MinClosingDebt.Value);
                if (query.MaxClosingDebt.HasValue)
                    groupedQuery = groupedQuery.Where(x =>
                        x.SettlementValueAmount - x.PaidAmount <= query.MaxClosingDebt.Value);
                if (query.Positive.HasValue && query.Positive.Value)
                    groupedQuery = groupedQuery.Where(x => x.SettlementValueAmount - x.PaidAmount != 0);
                if (!string.IsNullOrWhiteSpace(query.FullTextSearch))
                    groupedQuery = groupedQuery.Where(x =>
                        x.Code.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                        x.Name.ToLower().Contains(query.FullTextSearch.Trim().ToLower()));

                var data = await groupedQuery.GetPageAsync(query);

                var result = new Pagination<DebtReportViewModel>
                {
                    Page = data.Page,
                    TotalPages = data.TotalPages,
                    Size = data.Size,
                    NumberOfElements = data.NumberOfElements,
                    TotalElements = data.TotalElements,
                    Content = data.Content
                };

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<DebtReportViewModel>>(e);
            }
        }

        public async Task<Response<Pagination<DebtReportViewModel>>> GetPageDebtReportInvestor(
            DebtReportQueryModel query)
        {
            try
            {
                var predicate = BuildQueryDebtReport(query);
                var queryResult = _dbContext.sm_Contract.Include(x => x.Construction)
                    .ThenInclude(x => x.sm_Investor)
                    .ThenInclude(x => x.InvestorType);

                var contractsQuery = queryResult
                    .GroupBy(x => x.Construction.sm_Investor.Id)
                    .Select(g => new DebtReportViewModel
                    {
                        Code = g.First().Construction.sm_Investor.Code,
                        Name = g.First().Construction.sm_Investor.Name,
                        InvestorTypeName = g.First().Construction.sm_Investor.InvestorType.Name,
                        // SettlementValueAmount = g.Sum(x => x.SettlementValueAmount ?? 0),
                        PaidAmount = g.Sum(x => x.PaidAmount ?? 0),
                    });
                if (query.MinClosingDebt.HasValue)
                    contractsQuery = contractsQuery.Where(x =>
                        x.SettlementValueAmount - x.PaidAmount >= query.MinClosingDebt.Value);
                if (query.MaxClosingDebt.HasValue)
                    contractsQuery = contractsQuery.Where(x =>
                        x.SettlementValueAmount - x.PaidAmount <= query.MaxClosingDebt.Value);
                if (query.Positive.HasValue && query.Positive.Value)
                    contractsQuery = contractsQuery.Where(x => x.SettlementValueAmount - x.PaidAmount != 0);
                if (!string.IsNullOrWhiteSpace(query.FullTextSearch))
                    contractsQuery = contractsQuery.Where(x =>
                        x.Code.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                        x.Name.ToLower().Contains(query.FullTextSearch.Trim().ToLower()) ||
                        x.InvestorTypeName.ToLower().Contains(query.FullTextSearch.Trim().ToLower()));

                var data = await contractsQuery.GetPageAsync(query);

                var result = new Pagination<DebtReportViewModel>
                {
                    Page = data.Page,
                    TotalPages = data.TotalPages,
                    Size = data.Size,
                    NumberOfElements = data.NumberOfElements,
                    TotalElements = data.TotalElements,
                    Content = data.Content
                };

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception e)
            {
                Log.Error(e, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<DebtReportViewModel>>(e);
            }
        }
    }
}