using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Business.Services.ConstructionWeekReport;
using NSPC.Common;
using NSPC.Business.Services.ExecutionTeams;
using Microsoft.AspNetCore.Cors;

namespace NSPC.API.V1.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/construction")]
    [ApiExplorerSettings(GroupName = "Quản lý công trình/dự án")]
    public class ConstructionController : ControllerBase
    {
        private readonly IConstructionHandler _handler;
        private readonly IConstructionWeekReportHandler  _handlerWeekReport;

        public ConstructionController(IConstructionHandler handler,  IConstructionWeekReportHandler handlerWeekReport)
        {
            _handler = handler;
            _handlerWeekReport = handlerWeekReport;
        }

        /// <summary>
        /// Tạo công trình/dự án mới
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] ConstructionCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật công trình/dự án
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] ConstructionCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách công trình dự án
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách tổ thực hiện theo constructionId
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("execution-teams")]
        [ProducesResponseType(typeof(ResponsePagination<ExecutionTeamsViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetExecutionTeams([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<ExecutionTeamsQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetExecutionTeamsInConstruction(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Chi tiết công trình/dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.GetById(id, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa công trình dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Delete(id, user);

            return Helper.TransformData(result);
        }

        // #region Dashboard Construction All Analyze
        //
        // #region Thống kê theo từng dự án
        // /// <summary>
        // /// Thống kê theo từng dự án
        // /// </summary>
        // /// <returns></returns>
        // [HttpGet, Route("dashboard/{constructionId}")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> GetDashboard(Guid constructionId)
        // {
        //     var result = await _handler.DashboardConstruction(constructionId);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Thống kê theo tiêu chí
        // /// <summary>
        // /// Thống kê theo tiêu chí
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("analyze-all-dashboard")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> AnalyzeAllConstruction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.ConstructionAnalyzeAllDashboard(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Thống kê tất cả dự án theo trạng thái
        // /// <summary>
        // /// Thống kê tất cả dự án theo trạng thái
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("analyze-all-dashboard-by-status")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> AnalyzeAllConstructionByStatus([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.ChartConstructionByStatus(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Thống kê tỷ lệ vật tư có trong dự án
        // /// <summary>
        // /// Thống kê tỷ lệ vật tư có trong dự án
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("analyze-material-percent-in-construction")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> AnalyzeMaterialPercentInConstruction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.ChartPercentMaterialInConstruction(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Thống kê yêu cầu vật tư cần xử lý
        // /// <summary>
        // /// Thống kê yêu cầu vật tư cần xử lý
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("analyze-material-request-in-construction")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> AnalyzeMaterialRequestInConstruction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.ChartMaterialRequestInConstruction(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Thống kê yêu cầu tạm ứng cần xử lý
        // /// <summary>
        // /// Thống kê yêu cầu tạm ứng cần xử lý
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("analyze-advance-percent-in-construction")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> AnalyzeAdvanceRequestInConstruction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.ChartAdvanceRequestInConstruction(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Tổng hợp thu chi theo dự án
        // /// <summary>
        // /// Tổng hợp thu chi theo dự án
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("summary-cashbook-transaction")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionRevenueChart>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> SummaryCashbookTransaction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.SummaryCashbookTransactionByConstruction(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Số lượng vật tư thực tế và kế hoạch
        // /// <summary>
        // /// Số lượng vật tư thực tế và kế hoạch
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("summary-product")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> SummaryProduct([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.SummaryProductByConstruction(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #region Top giá trị 
        // /// <summary>
        // /// Top giá trị 
        // /// </summary>
        // /// <param name="size"></param>
        // /// <param name="page"></param>
        // /// <param name="filter"></param>
        // /// <param name="sort"></param>
        // /// <returns></returns>
        // [HttpGet, Route("summary-vat-amount")]
        // [ProducesResponseType(typeof(ResponsePagination<ConstructionViewModel>), StatusCodes.Status200OK)]
        // public async Task<IActionResult> SummaryVATAmount([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        // {
        //     var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);
        //
        //     sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
        //     filterObject.Sort = sort != null ? sort : filterObject.Sort;
        //     filterObject.Size = size;
        //     filterObject.Page = page;
        //
        //     var result = await _handler.TopVatAmountAnalyze(filterObject);
        //     return Helper.TransformData(result);
        // }
        // #endregion
        //
        // #endregion

        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("excel/export")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var user = Helper.GetRequestInfo(Request);
            
            var filterObject = JsonConvert.DeserializeObject<ConstructionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject, user);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }

        /// <summary>
        /// import từ excel
        /// </summary>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost, Route("excel/import")]
        [Authorize]
        [ProducesResponseType(typeof(Response<List<ConstructionViewModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Import(string path)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.Import(path, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Đánh dấu hoàn thành cho giai đoạn dự án
        /// </summary>
        /// <param name="constructionId"></param>
        /// <param name="templateStageId"></param>
        /// <returns></returns>
        [HttpPut("template-stage/{constructionId}/{templateStageId}/done")]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateTemplateStageIsDone(Guid constructionId, Guid templateStageId)
        {
            var result = await _handler.UpdateTemplateStageIsDone(constructionId, templateStageId);
            return Helper.TransformData(result);
        }
    }
}
