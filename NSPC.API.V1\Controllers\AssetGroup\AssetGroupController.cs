using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetGroup;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-group")]
[ApiExplorerSettings(GroupName = "Quản lý nhóm tài sản")]
[Authorize]
public class AssetGroupController
{
    private readonly IAssetGroupHandler _assetGroupHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetGroupHandler"></param>
    public AssetGroupController(IAssetGroupHandler assetGroupHandler)
    {
        _assetGroupHandler = assetGroupHandler;
    }

    /// <summary>
    /// Tạo nhóm tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Response<AssetGroupViewModel>>> Create([FromBody] AssetGroupCreateUpdateModel model)
    {
        var result = await _assetGroupHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật nhóm tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetGroupViewModel>>> Update(Guid id,
        [FromBody] AssetGroupCreateUpdateModel model)
    {
        var result = await _assetGroupHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách nhóm tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetGroupViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetGroupQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetGroupHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 nhóm tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetGroupViewModel>>> GetById(Guid id)
    {
        var result = await _assetGroupHandler.GetById(id);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 nhóm tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _assetGroupHandler.Delete(id);
        return Helper.TransformData(result);
    }
}