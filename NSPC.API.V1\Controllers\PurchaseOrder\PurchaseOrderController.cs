﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Server.HttpSys;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Business.Services.CashbookTransaction;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/purchase-orders")]
    [ApiExplorerSettings(GroupName = "Quản lý nhập hàng")]
    public class PurchaseOrderController : ControllerBase
    {
        private readonly IPurchaseOrderHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public PurchaseOrderController(IPurchaseOrderHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// T<PERSON><PERSON> phi<PERSON> nhập hàng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("PURCHASEORDER", RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] PurchaseOrderCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa phiếu nhập hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết phiếu nhập hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Import excel file
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        /*[HttpPost("import-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response<List<SaleOrderViewModel>>), StatusCodes.Status200OK)]
        public IActionResult ImportPreview([FromQuery] string path, string typePhieu)
        {
            return new ObjectResult(_handler.ImportPreview(path, typePhieu));
        }*/
        /// <summary>
        /// Export excel file
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        /*[EnableCors("AllowOrigin")]
        [HttpPost("export-excel/{id}")]
        [Authorize]
        [ProducesResponseType(typeof(Response<List<QuanLyPhieuCreateUpdateModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportExcel(Guid id)
        {
            var result = _handler.ExportExcel(id).Result.Data;
            byte[] file;


            using (MemoryStream memoryStream = new MemoryStream())
            {
                using (FileStream fileStream = new FileStream(result, FileMode.Open))
                {
                    await fileStream.CopyToAsync(memoryStream);
                }
                file = memoryStream.ToArray();

            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result));
        }*/


        /// <summary>
        /// Lấy danh sách phiếu
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("PURCHASEORDER", RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(ResponsePagination<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<PurchaseOrderQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật phiếu
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] PurchaseOrderCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);

            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Nhập hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("reciept/{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Reciept(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.Reciept(id, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Thanh toán đơn nhập hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("payment/{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChargePaymentOrder(Guid id, [FromBody] PurchaseOrderCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.ChargePaymentOrder(id, model, user);

            return Helper.TransformData(result);
        }


        /// <summary>
        /// Hủy đơn hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("reject-order/{id}")]
        [RightValidate("PURCHASEORDER", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<PurchaseOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RejectOrder(Guid id)
        {
            var result = await _handler.RejectOrder(id);

            return Helper.TransformData(result);
        }

        ///// <summary>
        ///// Lấy danh sách phiếu nhập
        ///// </summary>
        ///// <param name="size"></param>
        ///// <param name="page"></param>
        ///// <param name="filter"></param>
        ///// <param name="sort"></param>
        ///// <returns></returns>
        //[HttpGet, AllowAnonymous]
        //[Route("nhap")]
        //[ProducesResponseType(typeof(ResponsePagination<QuanLyPhieuViewModel>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> GetPhieuNhap(int size = 20, int page = 1, string filter = "{}", string sort = "")
        //{
        //    return Helper.TransformData(await Get(CodeTypeConstants.Don_Vi_Tinh, size, page, filter, sort, string.Empty));
        //}

        /// <summary>
        /// Cập nhật trạng thái
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        /*[HttpPut, Route("{id}/status")]
        [ProducesResponseType(typeof(Response<SaleOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatus(Guid id, [FromBody] TrangThaiPhieuNhapXuatModel model)
        {
            var result = await _handler.UpdateStatus(id, model);

            return Helper.TransformData(result);
        }*/

        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("export-list-to-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<PurchaseOrderQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }

}
