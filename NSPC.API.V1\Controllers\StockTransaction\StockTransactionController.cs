﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Business.Services.StockTransaction;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.StockTransaction
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/statiscal")]
    [ApiExplorerSettings(GroupName = "Báo cáo xuất nhập tồn kho")]
    public class StockTransactionController : ControllerBase
    {
        private readonly IStockTransactionHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public StockTransactionController(IStockTransactionHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo bản ghi xuất nhập tồn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] StockTransactionCreateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách xuat nhap kho
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("stock-transaction")]
        [ProducesResponseType(typeof(ResponsePagination<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<StockTransactionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }
        /// <summary>
        /// Lấy chi tiết lịch sử kho sản phẩm
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("stock-transaction/{id}")]
        [ProducesResponseType(typeof(Response<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy tồn sản phẩm theo kho và id
        /// </summary>
        /// <param name="id"></param>
        /// <param name="wareCode"></param>
        /// <returns></returns>
        [HttpGet, Route("stock-transaction/{id}/{wareCode}")]
        [ProducesResponseType(typeof(Response<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByIdAndWareCode(Guid id, string wareCode)
        {
            var result = await _handler.GetByIdAndWareCode(id, wareCode);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy tồn sản phẩm theo kho
        /// </summary>
        /// <param name="id"></param>
        /// <param name="wareCode"></param>
        /// <returns></returns>
        [HttpGet, Route("stock-transaction/get-by-ware-code/{wareCode}")]
        [ProducesResponseType(typeof(Response<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByWareCode(string wareCode)
        {
            var result = await _handler.GetByWareCode(wareCode);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy ra thao tác , lịch sử của từng kho
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("stock-transaction/stock-history")]
        [ProducesResponseType(typeof(Response<StockTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetStockHistoryOfEachWarehouse([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<StockTransactionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetStockHistoryOfEachWareHouse(filterObject);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("stock-transaction/export-list-to-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<StockTransactionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }
}
