﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
public enum NotificationStatus
{
    Mentioned,        // Nhắc đến bạn
    Overdue,           // Công việc quá hạn (dấu chấm than đỏ)
    Joined,             // Tham gia công việc (mũi tên cong xanh)
    Left,              // Rời khỏi công việc (mũi tên cong đỏ)
    Approved,           // G<PERSON>i duy<PERSON> (vòng tròn hồng)
    WarningSoonExpire,  // Cảnh báo sắp hết hạn (dấu chấm than vàng)
    StatusChanged       // Chuyển trạng thái công việc (hình người xanh)
}

namespace NSPC.Data
{
    /// <value>Bảng công việc con</value>
    [Table("sm_TaskNotification")]
    public class sm_TaskNotification : BaseTableService<sm_TaskNotification>
    {
        [Key]
        public Guid Id { get; set; }

        ///// <value>Tiêu đề thông báo</value>
        //[Required]
        //[MaxLength(250)]
        //public string Title { get; set; }

        ///// <value>Nội dung thông báo</value>
        //[Required]
        //public string Content { get; set; }

        /// <value>Loại thông báo (ví dụ: cảnh báo, nhắc nhở, thành công, v.v.)</value>
        [MaxLength(50)]
        public NotificationStatus NotificationStatus { get; set; }

        /// <value>Trạng thái xem</value>
        public bool IsRead { get; set; } = false;

        /// <value>Người nhận thông báo</value>
        public Guid? UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual idm_User idm_User { get; set; }


        /// <value>Loại người phê duyệt</value>
        public string? ApprovalType { get; set; }

        /// <value>ID công việc</value>
        public Guid? TaskId { get; set; }
        [ForeignKey(nameof(TaskId))]
        public sm_Task Task { get; set; }

        [ForeignKey("CreatedByUserId")]
        public virtual idm_User CreatedByUser { get; set; }
    }
}
