﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/warehouse-transfer-note")]
    [ApiExplorerSettings(GroupName = "Quản lý phiếu chuyển kho")]
    public class WarehouseTransferNoteController : Controller
    {
        private readonly IWarehouseTransferNoteHandler _handler;

        public WarehouseTransferNoteController(IWarehouseTransferNoteHandler handler)
        {
            _handler = handler;
        }
        
        /// <summary>
        /// Tạo phiếu chuyển kho
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<WarehouseTransferNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] WarehouseTransferNoteCreateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.Create(model, currentUser);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy chi tiết phiếu chuyển kho
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<WarehouseTransferNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật phiếu chuyển kho
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<WarehouseTransferNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] WarehouseTransferNoteCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);

            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy danh sách phiếu chuyển kho
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(ResponsePagination<WarehouseTransferNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<WarehouseTransferNoteQuery>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Hủy nhiều phiếu chuyển kho
        /// </summary> 
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPut, Route("cancel-multiple")]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> CancelMultipleAsync([FromQuery] List<Guid> ids)
        {
            var result = await _handler.CancelMultipleAsync(ids);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Chuyển kho
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("transfer-warehouse/{id}")]
        [RightValidate("WAREHOUSETRANSFERNOTE", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<WarehouseTransferNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> BalanceInventory(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.TransferWarehouse(id, user);
        
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("export-list-to-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<WarehouseTransferNoteQuery>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }
}
