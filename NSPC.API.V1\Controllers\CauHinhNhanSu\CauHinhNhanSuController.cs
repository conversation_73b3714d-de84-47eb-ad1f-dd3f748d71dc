﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.CauHinhNhanSu;
using NSPC.Business.Services.NhomVatTu;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.CauHinhNhanSu
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/cau-hinh-nhan-su")]
    [ApiExplorerSettings(GroupName = "Cấu hình nhân sự")]
    public class CauHinhNhanSuController: ControllerBase
    {
        private readonly ICauHinhNhanSuHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public CauHinhNhanSuController(ICauHinhNhanSuHandler handler)
        {
            _handler = handler;
        }

        ///// <summary>
        ///// <PERSON><PERSON><PERSON> c<PERSON>u hình nhân sự
        ///// </summary>
        ///// <param name="model"></param>
        ///// <returns></returns>
        //[HttpPost]
        //[ProducesResponseType(typeof(Response<CauHinhNhanSuViewModel>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> Create([FromBody] CauHinhNhanSuCreateUpdateModel model)
        //{
        //    var result = await _handler.Create(model);
        //    return Helper.TransformData(result);
        //}

        /// <summary>
        /// Cập nhật cấu hình nhân sự
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<CauHinhNhanSuViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] CauHinhNhanSuCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 cấu hình nhân sự
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<CauHinhNhanSuViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách cấu hình nhân sự
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<CauHinhNhanSuViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<CauHinhNhanSuQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        ///// <summary>
        ///// Xóa 1 cấu hình nhân sự
        ///// </summary>
        ///// <param name="id"></param>
        ///// <returns></returns>
        //[HttpDelete, Route("{id}")]
        //[ProducesResponseType(typeof(Response<CauHinhNhanSuViewModel>), StatusCodes.Status200OK)]
        //public async Task<IActionResult> Delete(Guid id)
        //{
        //    var result = await _handler.Delete(id);

        //    return Helper.TransformData(result);
        //}

        /// <summary>
        /// Seed cấu hình nhân sự
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> SeedCauHinh()
        {
            var result = await _handler.SeedCauHinh();

            return Helper.TransformData(result);
        }
    }
}
