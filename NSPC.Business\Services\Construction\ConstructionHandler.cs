using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using Serilog;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using Amazon.Runtime.Internal.Util;
using MongoDB.Driver.Linq;
using NSPC.Business.Services.CashbookTransaction;
using NSPC.Business.Services.Cata;
using NSPC.Data.Data.Entity.CashbookTransaction;
using NSPC.Data.Data.Entity.VatTu;
using NSPC.Data.Entity;
using NSPC.Business.Services.ConstructionActitvityLog;
using NSPC.Data.Data.Entity.AdvanceRequest;
using static NSPC.Common.Helper;
using NSPC.Business.Services.ExecutionTeams;
using NSPC.Data.Data.Entity.Contract;
using NSPC.Business.Services.ProjectTemplate;

namespace NSPC.Business.Services
{
    public class ConstructionHandler : IConstructionHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        private readonly IConstructionActivityLogHandler _constructionActivityLogHandler;

        public ConstructionHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper,
            IConstructionActivityLogHandler constructionActivityLogHandler)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _staticsFolder = Utils.GetConfig("StaticFiles:Folder");
            _constructionActivityLogHandler = constructionActivityLogHandler;
        }

        /// <summary>
        /// Tạo mới công trình/dự án
        /// </summary>
        /// <param name="model"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<ConstructionViewModel>> Create(ConstructionCreateUpdateModel model,
            RequestUser currentUser)
        {
            try
            {
                var userId = currentUser.UserId;
                var userName = currentUser.FullName;

                if (model.Code != null)
                {
                    if (_dbContext.sm_Construction.Any(x => x.Code == model.Code))
                        return Helper.CreateBadRequestResponse<ConstructionViewModel>(
                            $"Mã dự án ${model.Code} đã tồn tại!");
                }

                List<idm_User> allUser = new List<idm_User>();

                if (model.ExecutionTeams != null && model.ExecutionTeams.Count > 0)
                {
                    // Fill tất cả user
                    var allUserIds = model.ExecutionTeams.Select(x => x.EmployeeId).ToList();
                    allUser = await _dbContext.IdmUser.AsNoTracking()
                        .Where(x => allUserIds.Contains(x.Id))
                        .ToListAsync();
                }

                var entity = _mapper.Map<sm_Construction>(model);

                if (model.Code != null)
                {
                    entity.Code = model.Code;
                }
                else
                {
                    entity.Code = await GetNewCode(ConstructionConstants.PrefixCode.ConstructionCode);
                }

                entity.Id = Guid.NewGuid();
                entity.CreatedByUserId = currentUser.UserId;
                entity.CreatedByUserName = currentUser.FullName;
                entity.CreatedOnDate = DateTime.Now;
                entity.PriorityName = ConstructionConstants.FetchStatus(model.PriorityCode)?.Name;
                entity.DocumentStatusName = ConstructionConstants.FetchStatus(model.DocumentStatusCode)?.Name;
                entity.ExecutionStatusName = ConstructionConstants.FetchStatus(model.ExecutionStatusCode)?.Name;
                entity.StatusName = ConstructionConstants.FetchStatus(model.StatusCode)?.Name;
                var projectTemplate = await _dbContext.sm_ProjectTemplate
                    .Include(x => x.TemplateStages)
                    .FirstOrDefaultAsync(x => x.Id == model.ConstructionTemplateId);
                if (projectTemplate?.TemplateStages?.Any() == true)
                {
                    entity.TemplateStages = projectTemplate.TemplateStages.OrderBy(x => x.StepOrder)
                        .Select(stage => new jsonb_TemplateStage
                        {
                            Id = stage.Id,
                            StepOrder = stage.StepOrder,
                            Name = stage.Name,
                            Description = stage.Description,
                        }).ToList();
                }

                foreach (var item in entity.sm_ExecutionTeams)
                {
                    var user = allUser.FirstOrDefault(x => x.Id == item.EmployeeId);

                    if (user != null)
                    {
                        item.EmployeeName = user.Name;
                        item.MaPhongBan = user.MaPhongBan;
                        item.MaTo = user.MaTo;
                        item.EmployeeAvatarUrl = user.AvatarUrl;
                        item.ConstructionId = entity.Id;
                    }
                }

                _dbContext.sm_Construction.Add(entity);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động thêm mới công trình dự án vào bảng sm_ConstructionActivityLog

                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã tạo dự án",
                        CodeLinkDescription = $"{entity.Code} - {entity.Name}",
                        OrderId = entity.Id,
                        ConstructionId = entity.Id,
                    }, currentUser);

                    #endregion
                }

                Log.Information("Thêm mới thành công, Model: {@model}, UserId: {@userId}, UserName: {@userName}",
                    userId, model, userName);
                return Helper.CreateSuccessResponse(_mapper.Map<ConstructionViewModel>(entity), "Thêm mới thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<ConstructionViewModel>(ex);
            }
        }

        private async Task<string> GetNewCode(string defaultPrefix)
        {
            try
            {
                var code = defaultPrefix + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_Construction.AsNoTracking().Where(x => x.Code.Contains(code))
                    .OrderByDescending(x => x.CreatedOnDate).FirstOrDefaultAsync();

                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("123", ex);
                return string.Empty;
            }
        }

        /// <summary>
        /// Chi tiết công trình dự án
        /// </summary>
        /// <param name="id"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<ConstructionViewModel>> GetById(Guid id, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_Construction.AsNoTracking()
                    .Include(x => x.sm_ExecutionTeams)
                    .Include(x => x.sm_ProjectTemplate)
                    .Include(x => x.sm_ConstructionActivityLog)
                    .Include(x => x.sm_Investor)
                    .ThenInclude(x => x.InvestorType)
                    .Include(x => x.sm_IssueManagements)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (entity == null)
                {
                    Log.Information(
                        $"Không tìm thấy công trình với id {entity.Id}, UserId:  {currentUser.UserId}, userName: {currentUser.FullName}");
                    return Helper.CreateBadRequestResponse<ConstructionViewModel>("Không tìm thấy bản ghi");
                }

                Log.Information(
                    $"Lấy chi tiết công trình với id {entity.Id}, UserId: {currentUser.UserId}, userName: {currentUser.FullName}");
                return Helper.CreateSuccessResponse<ConstructionViewModel>(_mapper.Map<ConstructionViewModel>(entity));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<ConstructionViewModel>(ex);
            }
        }

        /// <summary>
        /// Cập nhật công trình/dự án
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<ConstructionViewModel>> Update(Guid id, ConstructionCreateUpdateModel model,
            RequestUser currentUser)
        {
            try
            {
                var userId = currentUser.UserId;
                var userName = currentUser.FullName;

                var entity = await _dbContext.sm_Construction
                    .Include(x => x.sm_ExecutionTeams)
                    .Include(x => x.sm_ProjectTemplate)
                    .Include(x => x.sm_ConstructionActivityLog)
                    .Include(x => x.sm_Investor)
                    .ThenInclude(x => x.InvestorType)
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (entity == null)
                {
                    Log.Information(
                        $"Không tìm thấy công trình với id {entity.Id},  UserId: {currentUser.UserId}, UserName: {currentUser.FullName}");
                    return Helper.CreateBadRequestResponse<ConstructionViewModel>("Không tìm thấy bản ghi");
                }

                List<idm_User> allUser = new List<idm_User>();

                if (model.ExecutionTeams != null && model.ExecutionTeams.Count > 0)
                {
                    // Fill tất cả user
                    var allUserIds = model.ExecutionTeams.Select(x => x.EmployeeId).ToList();
                    allUser = await _dbContext.IdmUser.AsNoTracking()
                        .Where(x => allUserIds.Contains(x.Id))
                        .ToListAsync();
                }

                if (model.Code != null)
                {
                    if (_dbContext.sm_Construction.Any(x => x.Code == model.Code && x.Id != id))
                        return Helper.CreateBadRequestResponse<ConstructionViewModel>(
                            $"Mã dự án {model.Code} đã tồn tại!");
                }


                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.FullName;
                entity.LastModifiedOnDate = DateTime.Now;

                if (model.Code != null)
                {
                    entity.Code = model.Code;
                }
                else
                {
                    entity.Code = await GetNewCode(ConstructionConstants.PrefixCode.ConstructionCode);
                }

                entity.Name = model.Name;
                entity.Note = model.Note;

                entity.StatusCode = model.StatusCode;
                entity.StatusName = ConstructionConstants.FetchStatus(model.StatusCode).Name;

                entity.PriorityCode = model.PriorityCode;
                entity.PriorityName = ConstructionConstants.FetchStatus(model.PriorityCode)?.Name;

                entity.DocumentStatusCode = model.DocumentStatusCode;
                entity.DocumentStatusName = ConstructionConstants.FetchStatus(model.DocumentStatusCode)?.Name;

                entity.ExecutionStatusCode = model.ExecutionStatusCode;
                entity.ExecutionStatusName = ConstructionConstants.FetchStatus(model.ExecutionStatusCode)?.Name;

                entity.CompletionByCompany = model.CompletionByCompany;
                entity.CompletionByInvestor = model.CompletionByInvestor;

                entity.OwnerTypeCode = model.OwnerTypeCode;
                entity.InvestorId = model.InvestorId;

                entity.DeliveryDate = model.DeliveryDate;

                entity.VoltageTypeCode = model.VoltageTypeCode;

                // Remove Old Execution Teams -> Re-add
                _dbContext.RemoveRange(entity.sm_ExecutionTeams);
                entity.sm_ExecutionTeams = new List<sm_ExecutionTeams>();

                if (model.ExecutionTeams != null && model.ExecutionTeams.Count > 0)
                {
                    foreach (var item in model.ExecutionTeams)
                    {
                        var executionTeamsEntity = _mapper.Map<sm_ExecutionTeams>(item);
                        entity.sm_ExecutionTeams.Add(executionTeamsEntity);

                        // Fill constructionId
                        executionTeamsEntity.ConstructionId = entity.Id;

                        // Fill info user
                        var user = allUser.FirstOrDefault(x => x.Id == item.EmployeeId);

                        if (user != null)
                        {
                            executionTeamsEntity.EmployeeName = user.Name;
                            executionTeamsEntity.MaPhongBan = user.MaPhongBan;
                            executionTeamsEntity.MaTo = user.MaTo;
                            executionTeamsEntity.EmployeeAvatarUrl = user.AvatarUrl;
                        }
                    }
                }

                // B3. Thêm lại item sau khi process xong
                foreach (var item in entity.sm_ExecutionTeams)
                {
                    _dbContext.sm_ExecutionTeams.Add(item);
                }

                _dbContext.sm_Construction.Update(entity);
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động cập nhật thông tin công trình vào bảng sm_ConstructionActivityLog

                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã cập nhật thông tin công trình",
                        CodeLinkDescription = $"{entity.Code} - {entity.Name}",
                        OrderId = entity.Id,
                        ConstructionId = entity.Id,
                    }, currentUser);

                    #endregion
                }

                Log.Information("Cập nhật thành công, UserId: {@userId}, UserName: {@userName}, Model: {@model}",
                    userId, userName, model);
                return Helper.CreateSuccessResponse<ConstructionViewModel>(_mapper.Map<ConstructionViewModel>(entity),
                    "Cập nhật thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<ConstructionViewModel>(ex);
            }
        }

        public async Task<Response<ConstructionViewModel>> Delete(Guid id, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_Construction
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (entity == null)
                {
                    Log.Information(
                        $"Không tìm thấy bản ghi với Id: {entity.Id}, UserId: {currentUser.UserId},  UserName: {currentUser.FullName}");
                    return Helper.CreateBadRequestResponse<ConstructionViewModel>("Không tìm thấy bản ghi");
                }

                _dbContext.sm_Construction.Remove(entity);
                await _dbContext.SaveChangesAsync();

                Log.Information(
                    $"Xoá thành công với Id: {{entity.Id}}, UserId: {currentUser.UserId}, UserName: {currentUser.FullName}");

                return Helper.CreateSuccessResponse<ConstructionViewModel>
                    (_mapper.Map<ConstructionViewModel>(entity), "Xoá thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<ConstructionViewModel>(ex);
            }
        }

        public async Task<Response<Pagination<ConstructionViewModel>>> GetPage(ConstructionQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);
                var queryResult = _dbContext.sm_Construction.AsNoTracking()
                    .Include(x => x.sm_ExecutionTeams)
                    .Include(x => x.sm_ProjectTemplate)
                    .Include(x => x.sm_ConstructionActivityLog)
                    .Include(x => x.sm_Investor)
                    .ThenInclude(x => x.InvestorType)
                    .Include(x => x.sm_IssueManagements)
                    .Where(predicate);

                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<ConstructionViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<ConstructionViewModel>>(ex);
            }
        }

        /// <summary>
        /// Lấy ra danh sách tổ thực hiện
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        public async Task<Response<Pagination<ExecutionTeamsViewModel>>> GetExecutionTeamsInConstruction(
            ExecutionTeamsQueryModel query)
        {
            try
            {
                var predicate = BuildQueryExecutionTeams(query);
                var queryResult = _dbContext.sm_ExecutionTeams.AsNoTracking().Where(predicate);

                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<ExecutionTeamsViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<ExecutionTeamsViewModel>>(ex);
            }
        }

        /// <summary>
        /// Build Query Execution Teams
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        private Expression<Func<sm_ExecutionTeams, bool>> BuildQueryExecutionTeams(ExecutionTeamsQueryModel query)
        {
            var predicate = PredicateBuilder.New<sm_ExecutionTeams>(true);

            predicate.And(x => x.ConstructionId == query.ConstructionId);

            if (!string.IsNullOrEmpty(query.MaPhongBan))
            {
                predicate.And(x => x.MaPhongBan == query.MaPhongBan);
            }

            if (query.DateRange != null && query.DateRange.Count() > 0)
            {
                if (query.DateRange[0].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date >= query.DateRange[0].Value.Date);

                if (query.DateRange[1].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date <= query.DateRange[1].Value.Date);
            }

            return predicate;
        }

        private Expression<Func<sm_Construction, bool>> BuildQuery(ConstructionQueryModel query)
        {
            var predicate = PredicateBuilder.New<sm_Construction>(true);

            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate = predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower())
                                               || s.Name.ToLower().Contains(query.FullTextSearch.ToLower())
                );

            if (!string.IsNullOrEmpty(query.StatusCode))
            {
                predicate.And(x => x.StatusCode == query.StatusCode);
            }

            if (!string.IsNullOrEmpty(query.ExecutionStatusCode))
            {
                predicate.And(x => x.ExecutionStatusCode == query.ExecutionStatusCode);
            }

            if (!string.IsNullOrEmpty(query.DocumentStatusCode))
            {
                predicate.And(x => x.DocumentStatusCode == query.DocumentStatusCode);
            }

            if (query.DeliveryDate.HasValue)
            {
                predicate.And(x => x.DeliveryDate.Value.Date == query.DeliveryDate.Value);
            }

            if (query.InvestorId.HasValue)
            {
                predicate.And(x => x.InvestorId == query.InvestorId);
            }

            if (query.DateRange != null && query.DateRange.Count() > 0)
            {
                if (query.DateRange[0].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date >= query.DateRange[0].Value.Date);

                if (query.DateRange[1].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date <= query.DateRange[1].Value.Date);
            }

            return predicate;
        }

        public async Task<Response<ConstructionDashboardViewModel>> DashboardConstruction(Guid constructionId)
        {
            try
            {
                var contracts = await _dbContext.sm_Contract.Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.ImplementationStatus }).Select(x => new
                    {
                        x.Key.ImplementationStatus, TotalCount = x.Count()
                    })
                    .ToListAsync();
                var allStatuses = new List<ImplementationStatus>
                {
                    ImplementationStatus.PendingApproval,
                    ImplementationStatus.NotImplemented,
                    ImplementationStatus.Approved,
                    ImplementationStatus.InProgress,
                    ImplementationStatus.OnHoldOrSuspended,
                };

                var contractData = allStatuses.Select(status => new
                {
                    statusName = status,
                    totalCount = contracts.FirstOrDefault(c => c.ImplementationStatus == status)?.TotalCount ?? 0
                }).ToList();

                var topAdvanceRequest = await _dbContext.sm_AdvanceRequest
                    .Where(x => x.ConstructionId == constructionId)
                    .OrderByDescending(x => x.TotalAmount)
                    .Select(x => new
                    {
                        x.Code,
                        x.TotalLineAmount
                    })
                    .ToListAsync();
                var materialRequest = await _dbContext.sm_MaterialRequestItem
                    .Include(x => x.sm_Product)
                    .Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.sm_Product.Name })
                    .Select(x => new
                    {
                        ProductName = x.Key.Name,
                        TotalCount = x.Count()
                    }).ToListAsync();
                var quantityMaterialRequest = await _dbContext.sm_MaterialRequestItem
                    .Include(x => x.sm_Product)
                    .Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.sm_Product.Name, x.RequestQuantity, x.PlannedQuantity })
                    .Select(x => new
                    {
                        ProductName = x.Key.Name,
                        x.Key.RequestQuantity,
                        x.Key.PlannedQuantity,
                    }).ToListAsync();
                var result = new ConstructionDashboardViewModel()
                {
                    ContractData = contractData,
                    AdvanceData = topAdvanceRequest,
                    MaterialData = materialRequest,
                    QuantityOfMaterialData = quantityMaterialRequest
                };
                return CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return CreateExceptionResponse<ConstructionDashboardViewModel>(ex);
            }
        }

        // Thống kê theo tiêu chí
        public async Task<Response<ConstructionAnalyzeAll>> ConstructionAnalyzeAllDashboard(
            ConstructionQueryModel query)
        {
            try
            {
                var newConstructionAnalyzeAll = new ConstructionAnalyzeAll();

                // List construction
                var listConstruction = await GetPage(query);

                // if (listConstruction.Data.Content.Count() > 0)
                // {
                //     newConstructionAnalyzeAll.TotalVatAmount = listConstruction.Data.Content.Sum(x => x.TotalAmount);
                //     newConstructionAnalyzeAll.TotalRemainingAmount = listConstruction.Data.Content.Sum(x => x.RemainingAmount);
                //     newConstructionAnalyzeAll.TotalPaymentAmount = listConstruction.Data.Content.Sum(x => x.TotalPaymentAmount);
                //     newConstructionAnalyzeAll.TotalReceiptAmount =  listConstruction.Data.Content.Sum(x => x.TotalReceiptAmount);
                //     newConstructionAnalyzeAll.TotalProfitLoss =
                //         listConstruction.Data.Content.Sum(x => x.ProfitLossAmount);
                // }

                return Helper.CreateSuccessResponse(newConstructionAnalyzeAll);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Thống kê tất cả dự án theo trạng thái
        public async Task<Response<List<ConstructionChartByStatus>>> ChartConstructionByStatus(
            ConstructionQueryModel query)
        {
            try
            {
                return null;
                // List<ConstructionChartByStatus> newConstructionChartByStatus = new List<ConstructionChartByStatus> ();
                //
                // var allStatuses = new List<string>
                // {
                //     ConstructionConstants.StatusCode.NOW,
                //     ConstructionConstants.StatusCode.IN_PROGRESS,
                //     ConstructionConstants.StatusCode.COMPLETED,
                //     ConstructionConstants.StatusCode.CANCELLED
                // };
                //
                // // List construction
                // var listConstruction = await GetPage(query);
                //
                // if (listConstruction.Data.Content.Count() > 0)
                // {
                //     foreach (var item in allStatuses)
                //     {
                //         newConstructionChartByStatus.Add(new ConstructionChartByStatus()
                //         {
                //             Name = ConstructionConstants.FetchStatus(item).Name,
                //             Value = listConstruction.Data.Content
                //                 .Where(x => x.StatusCode == item)
                //                 .ToList().Count,
                //         });
                //     }
                // }
                //
                // return Helper.CreateSuccessResponse(newConstructionChartByStatus);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Thống kê tỷ lệ vật tư có trong dự án 
        public async Task<Response<List<ConstructionChartByStatus>>> ChartPercentMaterialInConstruction(
            ConstructionQueryModel query)
        {
            try
            {
                return null;
                // List<ConstructionChartByStatus> newConstructionChartByStatus = new List<ConstructionChartByStatus>();
                // List<Object> allMaterialInConstruction =  new List<Object>();
                //
                // // List construction
                // var listConstruction = await GetPage(query);
                //
                // if (listConstruction.Data.Content.Count() > 0)
                // {
                //     foreach (var item in listConstruction.Data.Content)
                //     {
                //         foreach (var items in item.ConstructionItems)
                //         {
                //             allMaterialInConstruction.Add(items);
                //         }
                //     }
                //     
                //     var groupArr = _mapper.Map<List<ConstructionItemViewModel>>(allMaterialInConstruction).GroupBy(x => new { x.ProductId, x.Code, x.Name, x.Unit}).Select(
                //         x => new ConstructionItemViewModel()
                //         {
                //             ProductId = x.Key.ProductId,
                //             Code = x.Key.Code,
                //             Name = x.Key.Name,
                //             Unit = x.Key.Unit,
                //             PlannedQuantity = x.Sum(x => x.PlannedQuantity),
                //             RealQuantity = x.Sum(x => x.RealQuantity),
                //         });
                //
                //     foreach (var item in groupArr)
                //     {
                //         if (item.PlannedQuantity != 0)
                //         {
                //             newConstructionChartByStatus.Add(new ConstructionChartByStatus()
                //             {
                //                 Name = item.Name,
                //                 Value = Math.Round(((item.RealQuantity / item.PlannedQuantity) * 100), 2)
                //             });  
                //         }
                //         else
                //         {
                //             newConstructionChartByStatus.Add(new ConstructionChartByStatus()
                //             {
                //                 Name = item.Name,
                //                 Value = 0,
                //             }); 
                //         }
                //     }
                //     
                // }
                //
                // return Helper.CreateSuccessResponse(newConstructionChartByStatus);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Thống kê yêu cầu vật tư cần xử lý
        public async Task<Response<List<ConstructionChartByStatus>>> ChartMaterialRequestInConstruction(
            ConstructionQueryModel query)
        {
            try
            {
                List<ConstructionChartByStatus> newConstructionChartByStatus = new List<ConstructionChartByStatus>();

                var allStatuses = new List<string>
                {
                    MaterialRequestConstants.FetchStatus(MaterialRequestConstants.StatusCode.APPROVE).Name,
                    MaterialRequestConstants.FetchStatus(MaterialRequestConstants.StatusCode.DRAFT).Name,
                    MaterialRequestConstants.FetchStatus(MaterialRequestConstants.StatusCode.PENDING_APPROVE).Name,
                };

                foreach (var itemStatus in allStatuses)
                {
                    newConstructionChartByStatus.Add(new ConstructionChartByStatus()
                    {
                        Name = itemStatus,
                        Value = _dbContext.sm_MaterialRequest
                            .Where(x => x.StatusName == itemStatus)
                            .ToList().Count,
                    });
                }

                return Helper.CreateSuccessResponse(newConstructionChartByStatus);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Thống kê yêu cầu tạm ứng cần xử lý
        public async Task<Response<List<ConstructionChartByStatus>>> ChartAdvanceRequestInConstruction(
            ConstructionQueryModel query)
        {
            try
            {
                List<ConstructionChartByStatus> newConstructionChartByStatus = new List<ConstructionChartByStatus>();

                var allStatuses = new List<string>
                {
                    AdvanceRequestConstants.StatusName.DRATF,
                    AdvanceRequestConstants.StatusName.PENDING_APPROVAL,
                    AdvanceRequestConstants.StatusName.APPROVED,
                };

                foreach (var itemStatus in allStatuses)
                {
                    newConstructionChartByStatus.Add(new ConstructionChartByStatus()
                    {
                        Name = itemStatus,
                        Value = _dbContext.sm_AdvanceRequest
                            .Where(x => x.StatusName == itemStatus)
                            .ToList().Count,
                    });
                }

                return Helper.CreateSuccessResponse(newConstructionChartByStatus);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Tổng hợp thu chi theo dự án
        public async Task<Response<ConstructionRevenueChart>> SummaryCashbookTransactionByConstruction(
            ConstructionQueryModel query)
        {
            try
            {
                return null;
                // List<string> listConstructionName = new List<string> ();
                // List<decimal> listConstructionReceiptAmount = new List<decimal> ();
                // List<decimal> listConstructionPaymentAmount = new List<decimal> ();
                //
                // var newConstructionRevenueChart = new ConstructionRevenueChart(); 
                //
                // // List construction
                // var listConstruction = await GetPage(query);
                //
                // if (listConstruction.Data.Content.Count() > 0)
                // {
                //     foreach (var item in listConstruction.Data.Content)
                //     {
                //         listConstructionName.Add(item.Name);
                //         listConstructionReceiptAmount.Add(item.TotalReceiptAmount);
                //         listConstructionPaymentAmount.Add(item.TotalPaymentAmount);
                //     }
                // }
                //
                // newConstructionRevenueChart.ListNameOfConstruction = listConstructionName;
                // newConstructionRevenueChart.ListPaymentAmountInConstruction = listConstructionPaymentAmount;
                // newConstructionRevenueChart.ListReceiptAmountInConstruction = listConstructionReceiptAmount;
                //
                // return Helper.CreateSuccessResponse(newConstructionRevenueChart);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Số lượng vật tư thực tế và kế hoạch
        public async Task<Response<MaterialPlanChart>> SummaryProductByConstruction(ConstructionQueryModel query)
        {
            try
            {
                return null;
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Top giá trị 
        public async Task<Response<List<TopAmountAnalyze>>> TopVatAmountAnalyze(ConstructionQueryModel query)
        {
            try
            {
                return null;
                // List<TopAmountAnalyze> topAmountAnalyze = new List<TopAmountAnalyze>();
                // // List construction
                // var listConstruction = await GetPage(query);
                //
                // if (listConstruction.Data.Content.Count() > 0)
                // {
                //     foreach (var item in listConstruction.Data.Content)
                //     {
                //         topAmountAnalyze.Add(new TopAmountAnalyze()
                //         {
                //             Title = item.Name,
                //             Amount = item.TotalAmount,
                //         });
                //     }
                // }
                //
                // var top5AmountAnalyze = topAmountAnalyze.Take(5).OrderByDescending(x => x.Amount).ToList();
                //
                // return Helper.CreateSuccessResponse(top5AmountAnalyze);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                throw;
            }
        }

        // Top khách hàng chủ đầu tư theo công nợ
        public async Task<Response<List<TopAmountAnalyze>>> TopCashbookTransactionAnalyze(ConstructionQueryModel query)
        {
            try
            {
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// Hàm xuất file danh sách đơn nhập hàng excel theo query
        /// </summary>
        /// <param name="query"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<Response<string>> ExportListToExcel(ConstructionQueryModel query, RequestUser currentUser)
        {
            try
            {
                // Tạo predicate để lọc dựa trên các tham số trong query
                var predicate = BuildQuery(query);

                // Lấy danh sách công trình từ cơ sở dữ liệu dựa trên lọc và phân trang
                var constructionEntity = await _dbContext.sm_Construction.AsNoTracking()
                    .Include(x => x.sm_ExecutionTeams)
                    .Include(x => x.sm_ProjectTemplate)
                    .Include(x => x.sm_ConstructionActivityLog)
                    .Include(x => x.sm_Investor)
                    .ThenInclude(x => x.InvestorType)
                    .Where(predicate)
                    .OrderByDescending(x => x.CreatedOnDate)
                    .GetPageAsync(query);

                // Kiểm tra nếu không có dữ liệu trả về trong trang hiện tại
                if (constructionEntity == null || constructionEntity.Content == null ||
                    constructionEntity.Content.Count == 0)
                    return Helper.CreateNotFoundResponse<string>("Không có công trình nào tồn tại trong hệ thống.");

                // Đặt tên file và đường dẫn template
                var fileName = $"Danh sách công trình_{DateTime.Now:yyyyMMddHHmmss}_{Guid.NewGuid()}.xlsx";
                var filePath = Path.Combine(_staticsFolder, fileName);
                var templatePath = Path.Combine(_staticsFolder, "excel-template/ConstructionTemplate.xlsx");

                if (string.IsNullOrEmpty(templatePath) || !File.Exists(templatePath))
                    return Helper.CreateBadRequestResponse<string>("Không tìm thấy file template");

                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                // Mở template Excel và điền dữ liệu vào file
                using (var package = new ExcelPackage(new FileInfo(templatePath)))
                {
                    var worksheet = package.Workbook.Worksheets[0]; // Sử dụng worksheet đầu tiên

                    // Đặt độ rộng cố định cho cột A (cột STT) là 8
                    worksheet.Column(1).Width = 8;

                    // Điền dữ liệu vào bảng Excel (giả sử bắt đầu từ hàng thứ 4)
                    int startRow = 4;
                    int index = 1;

                    foreach (var order in constructionEntity.Content)
                    {
                        worksheet.Cells[startRow, 1].Value = index; // STT
                        worksheet.Cells[startRow, 2].Value = order.CreatedOnDate.ToString("dd/MM/yyyy"); // Ngày tạo
                        worksheet.Cells[startRow, 3].Value =
                            order.LastModifiedOnDate?.ToString("dd/MM/yyyy"); // Ngày cập nhật
                        worksheet.Cells[startRow, 4].Value = order.Code; // Mã công trình
                        worksheet.Cells[startRow, 5].Value = $"{order.Name} " +
                                                             $" - {CodeTypeCollection.Instance.FetchCode(order.VoltageTypeCode, LanguageConstants.Default, order.TenantId).Title} " +
                                                             $" - {ConstructionConstants.FetchStatus(order.PriorityCode).Name}"; // Tên công trình
                        worksheet.Cells[startRow, 6].Value =
                            $"{order.sm_Investor.Name} - {order.sm_Investor.InvestorType.Code}"; // CĐT/BQLDA

                        List<string> executionParticipantTeams = new List<string>();
                        List<string> executionFollowerTeams = new List<string>();
                        List<string> executionProjectTeams = new List<string>();

                        if (order.sm_ExecutionTeams != null && order.sm_ExecutionTeams.Count > 0)
                        {
                            executionParticipantTeams = order.sm_ExecutionTeams.Where(x => x.UserType == "participants")
                                .Select(x => x.EmployeeName).Distinct().ToList();
                            executionFollowerTeams = order.sm_ExecutionTeams.Where(x => x.UserType == "follower")
                                .Select(x => x.EmployeeName).Distinct().ToList();

                            var executionTeams = await GetExecutionTeamsInConstruction(new ExecutionTeamsQueryModel()
                            {
                                ConstructionId = order.Id
                            });

                            if (executionTeams.Data.Content != null && executionTeams.Data.Content.Count > 0)
                            {
                                executionProjectTeams = executionTeams.Data.Content
                                    .Where(x => x.ToThucHien != null)
                                    .Select(x => x.ToThucHien?.Title).Distinct().ToList();
                            }
                        }

                        worksheet.Cells[startRow, 7].Value = string.Join(", ", executionProjectTeams);
                        worksheet.Cells[startRow, 8].Value = string.Join(", ", executionFollowerTeams);
                        worksheet.Cells[startRow, 9].Value = string.Join(", ", executionParticipantTeams);
                        worksheet.Cells[startRow, 10].Value = order.DeliveryDate?.ToString("dd/MM/yyyy");
                        worksheet.Cells[startRow, 11].Value = order.StatusName; // Tình trạng dự án
                        worksheet.Cells[startRow, 12].Value = order.ExecutionStatusName; // Tình hình thực hiện
                        worksheet.Cells[startRow, 13].Value = order.DocumentStatusName; // Tình trạng hồ sơ
                        worksheet.Cells[startRow, 14].Value = order.sm_ProjectTemplate.Name; // Template dự án

                        startRow++;
                        index++;
                    }

                    int lastDataRow = startRow - 1;

                    // Thêm đường viền cho các ô đã điền dữ liệu
                    using (var range =
                           worksheet.Cells[4, 1, lastDataRow,
                               14]) // điều chỉnh cột cuối (18) tùy theo số lượng cột bạn có
                    {
                        range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                    }

                    // Xóa các dòng thừa sau khi điền dữ liệu
                    int totalRows = worksheet.Dimension.End.Row;
                    if (totalRows > lastDataRow)
                    {
                        worksheet.DeleteRow(lastDataRow + 1, totalRows - lastDataRow);
                    }

                    // Tự động điều chỉnh kích thước các cột từ cột thứ hai đến cột cuối cùng
                    worksheet.Cells[1, 1, worksheet.Dimension.End.Row, worksheet.Dimension.End.Column].AutoFitColumns();

                    // Lưu file Excel đã điền dữ liệu
                    await package.SaveAsAsync(new FileInfo(filePath));
                }

                Log.Information(
                    $"Xuất file thành công, UserId: {currentUser.UserId}, UserName: {currentUser.FullName}");
                return Helper.CreateSuccessResponse<string>(filePath, "Xuất file thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message, "Xuất file thất bại");
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<string>(ex);
            }
        }

        public async Task<Response<ConstructionViewModel>> UpdateTemplateStageIsDone(Guid constructionId,
            Guid templateStageId)
        {
            try
            {
                var entity = await _dbContext.sm_Construction.FirstOrDefaultAsync(x => x.Id == constructionId);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<ConstructionViewModel>(
                        "Công trình không tồn tại trong hệ thống!");

                var stage = entity.TemplateStages?.FirstOrDefault(s => s.Id == templateStageId);
                if (stage == null)
                    return Helper.CreateNotFoundResponse<ConstructionViewModel>(
                        "Giai đoạn không tồn tại trong công việc!");

                entity.TemplateStages = entity.TemplateStages
                    .Select(stage =>
                    {
                        if (stage.Id == templateStageId)
                            stage.IsDone = true;
                        return stage;
                    })
                    .ToList();

                await _dbContext.SaveChangesAsync();

                var result = _mapper.Map<ConstructionViewModel>(entity);
                return Helper.CreateSuccessResponse(result, "Cập nhật trạng thái giai đoạn thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: taskId: {@taskId}, templateStageId: {@templateStageId}", constructionId,
                    templateStageId);
                return Helper.CreateExceptionResponse<ConstructionViewModel>(ex);
            }
        }

        public async Task<Response<List<ConstructionViewModel>>> Import(string path, RequestUser currentUser)
        {
            try
            {
                var filePath = Path.Combine(_staticsFolder, path);
                if (string.IsNullOrEmpty(path) || !File.Exists(filePath))
                    return Helper.CreateBadRequestResponse<List<ConstructionViewModel>>("Đường dẫn không tồn tại");

                // // Xoá toàn bộ dữ liệu hiện tại trong bảng Construction và ExecutionTeams
                // var allConstructions = _dbContext.sm_Construction.ToList();
                // if (allConstructions.Any())
                // {
                //     _dbContext.sm_Construction.RemoveRange(allConstructions);
                //     await _dbContext.SaveChangesAsync();
                // }

                var result = new List<ConstructionViewModel>();
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                using (var package = new ExcelPackage(filePath))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault(x => x.Name == "Danh sách công trình");
                    if (worksheet == null)
                        return Helper.CreateBadRequestResponse<List<ConstructionViewModel>>(
                            "Không tìm thấy sheet 'Danh sách công trình'");

                    int startRow = 4;
                    int lastRow = worksheet.Dimension.End.Row;

                    for (int rowIndex = startRow; rowIndex <= lastRow; rowIndex++)
                    {
                        var sttCell = worksheet.Cells[rowIndex, 1];
                        if (sttCell == null || sttCell.Value == null) break;

                        var createdDateStr = worksheet.Cells[rowIndex, 2].Text;
                        var modifiedDateStr = worksheet.Cells[rowIndex, 3].Text;
                        var code = worksheet.Cells[rowIndex, 4].Text?.Trim();
                        var name = worksheet.Cells[rowIndex, 5].Text?.Trim();
                        var investorValue = worksheet.Cells[rowIndex, 6].Text?.Trim();
                        var projects = worksheet.Cells[rowIndex, 7].Text?.Trim();
                        var participants = worksheet.Cells[rowIndex, 8].Text?.Trim();
                        var followers = worksheet.Cells[rowIndex, 9].Text?.Trim();
                        var deliveryDateStr = worksheet.Cells[rowIndex, 10].Text?.Trim();
                        var statusName = worksheet.Cells[rowIndex, 11].Text?.Trim();
                        var execStatusName = worksheet.Cells[rowIndex, 12].Text?.Trim();
                        var docStatusName = worksheet.Cells[rowIndex, 13].Text?.Trim();
                        var templateName = worksheet.Cells[rowIndex, 14].Text?.Trim();

                        if (result.Any(x => x.Code == code))
                            return Helper.CreateBadRequestResponse<List<ConstructionViewModel>>(
                                $"Mã công trình bị trùng: {code}");

                        string investorCode = null,
                            ownerTypeCode = null,
                            voltageTypeName = null,
                            constructionName = null,
                            priorityName = null;
                        List<ExecutionTeamsCreateModel> executionTeams = new List<ExecutionTeamsCreateModel>();
                        if (!string.IsNullOrWhiteSpace(investorValue))
                        {
                            var parts = investorValue.Split('-');
                            investorCode = parts[0].Trim();
                            ownerTypeCode = parts.Length > 1 ? parts[1].Trim() : null;
                        }

                        if (!string.IsNullOrWhiteSpace(name))
                        {
                            var parts = name.Split('-');
                            constructionName = parts[0].Trim();
                            voltageTypeName = parts.Length > 1 ? parts[1].Trim() : null;
                            priorityName = parts.Length > 2 ? parts[2].Trim() : null;
                        }

                        var investorEntity = _dbContext.sm_Investor.AsNoTracking()
                            .FirstOrDefault(x => x.Name == investorCode);

                        var templateEntity = _dbContext.sm_ProjectTemplate
                            .FirstOrDefault(x => x.Name == templateName);

                        var item = new ConstructionCreateUpdateModel
                        {
                            Code = code,
                            Name = constructionName,
                            InvestorId = investorEntity?.Id ?? Guid.NewGuid(),
                            OwnerTypeCode = ownerTypeCode,
                            StatusName = statusName,
                            StatusCode = ConstructionConstants.FetchCode(statusName).Code,
                            ExecutionStatusName = execStatusName,
                            ExecutionStatusCode = ConstructionConstants.FetchCode(execStatusName).Code,
                            DocumentStatusName = docStatusName,
                            DocumentStatusCode = ConstructionConstants.FetchCode(docStatusName).Code,
                            ConstructionTemplateId = templateEntity?.Id ?? Guid.NewGuid(),
                            DeliveryDate = DateTime.TryParse(deliveryDateStr, out var dd) ? dd : DateTime.Now,
                        };

                        var voltageTypeEntity = _dbContext.sm_CodeType.FirstOrDefault(x => x.Title == voltageTypeName);

                        item.PriorityCode = ConstructionConstants.FetchCode(priorityName).Code ?? null;
                        item.VoltageTypeCode = voltageTypeEntity != null ? voltageTypeEntity.Code : null;

                        /// Lưu danh sách nhân sự tham gia vào mảng executionItems
                        foreach (var items in participants.Split(", "))
                        {
                            var userEntity = _dbContext.IdmUser.FirstOrDefault(x => x.Name == items);

                            if (userEntity != null)
                            {
                                executionTeams.Add(new ExecutionTeamsCreateModel()
                                {
                                    EmployeeId = userEntity.Id,
                                    UserType = "participants"
                                });
                            }
                        }

                        item.ExecutionTeams = executionTeams;

                        /// Add nốt danh sách người theo dõi vào mảng ExecutionItems mới đã lưu bên trên
                        foreach (var items in followers.Split(", "))
                        {
                            var userEntity = _dbContext.IdmUser.FirstOrDefault(x => x.Name == items);

                            if (userEntity != null)
                            {
                                item.ExecutionTeams.Add(new ExecutionTeamsCreateModel()
                                {
                                    EmployeeId = userEntity.Id,
                                    UserType = "follower"
                                });
                            }
                        }

                        var data = await Create(item, currentUser);

                        if (data.IsSuccess)
                        {
                            result.Add(data.Data);
                        }
                        else
                        {
                            Log.Information(
                                $"Lỗi import file, UserName: {currentUser.FullName}, UserId: {currentUser.UserId}, Message: {data.Message}");
                            return Helper.CreateBadRequestResponse<List<ConstructionViewModel>>(data.Message);
                        }
                    }
                }

                Log.Information(
                    $"Import thành công, UserName: {currentUser.FullName}, UserId: {currentUser.UserId}, path: {path}");
                return new Response<List<ConstructionViewModel>>(System.Net.HttpStatusCode.OK, result,
                    $"Nhập file excel thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Lỗi khi import công trình từ Excel");
                Log.Information("Params: Path: {@path}", path);
                return Helper.CreateExceptionResponse<List<ConstructionViewModel>>(ex);
            }
        }
    }
}