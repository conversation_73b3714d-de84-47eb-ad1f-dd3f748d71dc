using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.AssetLiquidationSheet;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetLiquidationSheet;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-liquidation")]
[ApiExplorerSettings(GroupName = "Quản lý phiếu thanh lý tài sản")]
[Authorize]
public class AssetLiquidationSheetController
{
    private readonly IAssetLiquidationSheetHandler _liquidationSheetHandler;

    public AssetLiquidationSheetController(IAssetLiquidationSheetHandler liquidationSheetHandler)
    {
        _liquidationSheetHandler = liquidationSheetHandler;
    }

    /// <summary>
    /// Tạo phiếu thanh lý
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Response<AssetLiquidationSheetViewModel>>> Create(
        [FromBody] AssetLiquidationSheetCreateUpdateModel model)
    {
        var result = await _liquidationSheetHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật phiếu thanh lý
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetLiquidationSheetViewModel>>> Update(Guid id,
        [FromBody] AssetLiquidationSheetCreateUpdateModel model)
    {
        var result = await _liquidationSheetHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách phiếu thanh lý
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetLiquidationSheetViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetLiquidationSheetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _liquidationSheetHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 phiếu thanh lý
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetLiquidationSheetViewModel>>> GetById(Guid id)
    {
        var result = await _liquidationSheetHandler.GetById(id);

        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy số lượng phiếu thanh lý theo từng trạng thái
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    [HttpGet, Route("count-by-status")]
    public async Task<ActionResult<Response<Dictionary<string, int>>>> CountByStatus([FromQuery] int size = 20,
        [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
    {
        var filterObject = JsonConvert.DeserializeObject<AssetLiquidationSheetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _liquidationSheetHandler.CountByStatus(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 phiếu thanh lý
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _liquidationSheetHandler.Delete(id);

        return Helper.TransformData(result);
    }
}