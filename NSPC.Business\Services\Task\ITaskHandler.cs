﻿using NSPC.Common;

namespace NSPC.Business.Services.WorkItem
{
    public interface ITaskHandler
    {
        Task<Response<TaskViewModel>> Create(TaskCreateUpdateModel model);
        Task<Response<TaskViewModel>> Update(Guid id, TaskCreateUpdateModel model);
        Task<Response<TaskViewModel>> GetById(Guid id);
        Task<Response<Pagination<TaskViewModel>>> GetPage(TaskQueryModel query);
        Task<Response<TaskViewModel>> Delete(Guid id);
        Task<Response> DeleteMany(List<Guid> ids);
        Task<Response<TaskViewModel>> UpdateStatus(Guid id, string status, string description);
        Task<Response<Pagination<TaskViewModel>>> GetPageByConstructionId(Guid constructionId, TaskQueryModel query);
        Task<Response<List<TaskViewModel>>> UpdateStatusMany(List<Guid> ids, string status, string description);
    }
}
