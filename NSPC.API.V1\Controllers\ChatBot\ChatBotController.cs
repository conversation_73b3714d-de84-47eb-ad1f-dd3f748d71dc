using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.ChatBot;

namespace NSPC.API.V1.Controllers.ChatBot;

[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/chatbot")]
[ApiExplorerSettings(GroupName = "Chatbot")]
[Authorize]
public class ChatBotController : ControllerBase
{
    private readonly IChatBotHandler _chatBotHandler;

    public ChatBotController(IChatBotHandler chatBotHandler)
    {
        _chatBotHandler = chatBotHandler;
    }

    /// <summary>
    /// User chat with AI about specific customer
    /// </summary>
    /// <returns></returns>
    [HttpPost, Route("chat")]
    public async Task Chat([FromBody] ChatBotPromptModel model)
    {
        Response.ContentType = "text/plain; charset=utf-8";
        Response.StatusCode = StatusCodes.Status200OK;

        var result = await _chatBotHandler.GetResponseFromBot(model, Response.Body);

        if (!result.IsSuccess)
        {
            Response.ContentType = "application/json; charset=utf-8";
            Response.StatusCode = (int)result.Code;
            await Response.WriteAsync(JsonConvert.SerializeObject(result));
        }
    }
}