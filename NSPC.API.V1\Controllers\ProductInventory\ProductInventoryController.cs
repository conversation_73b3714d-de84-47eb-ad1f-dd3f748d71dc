﻿using Microsoft.AspNetCore.Mvc;
using NSPC.Business.Services;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    public class ProductInventoryController: ControllerBase
    {
        private readonly IProductInventoryHandler _handler;

        public ProductInventoryController(IProductInventoryHandler handler)
        {
            _handler = handler;
        }
        
        /// <summary>
        /// Tạo bản ghi ProductInventory
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<ProductInventoryViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] ProductInventoryCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }
    } 
}

