﻿using NSPC.Common;
using NSPC.Data;
namespace NSPC.Business.Services.WorkItem
{
    public class TaskViewModel
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public DateTime StartDateTime { get; set; }
        public DateTime EndDateTime { get; set; }
        public string Description { get; set; }
        public string PriorityLevel { get; set; }
        public string? Status { get; set; }
        public List<SubTaskViewModel> SubTasks { get; set; }
        public List<TaskApproverViewModel> Approvers { get; set; }
        public List<TaskExecutorViewModel> Executors { get; set; }
        public ConstructionViewModel Construction { get; set; }
        public List<AttachmentViewModel> Attachments { get; set; }
    }
    public class TaskCreateUpdateModel
    {
        public Guid? Id { get; set; }
        public string Name { get; set; }
        public DateTime? StartDateTime { get; set; }
        public DateTime? EndDateTime { get; set; }
        public string Description { get; set; }
        public string? PriorityLevel { get; set; }
        public Guid? ConstructionId { get; set; }
        public List<Guid> ApproverIds { get; set; }
        public List<Guid> ExecutorIds { get; set; }
        public List<jsonb_Attachment> Attachments { get; set; }
        public List<SubTaskCreateUpdateModel> SubTasks { get; set; }
    }
    public class TaskQueryModel : PaginationRequest
    {
        public Guid ConstructionId { get; set; }
    }
    public class SubTaskCreateUpdateModel
    {
        public string Name { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? DueDate { get; set; }
        public List<jsonb_Attachment> Attachments { get; set; }
        public Guid TaskId { get; set; }
        public List<Guid> ExecutorIds { get; set; }
    }
    public class SubTaskViewModel
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? DueDate { get; set; }
        public List<AttachmentViewModel> Attachments { get; set; }
    }
    public class TaskExecutorViewModel
    {
        public Guid Id { get; set; }
        public UserModel Idm_User { get; set; }
    }
    public class TaskApproverViewModel
    {
        public Guid Id { get; set; }
        public UserModel Idm_User { get; set; }
    }
}
