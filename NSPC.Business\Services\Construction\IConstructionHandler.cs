using NSPC.Business.Services.CashbookTransaction;
using NSPC.Common;
using static NSPC.Common.Helper;
using NSPC.Business.Services.ExecutionTeams;


namespace NSPC.Business.Services
{
    public interface IConstructionHandler
    {
        #region Process Construction Handler
        Task<Response<ConstructionViewModel>> Create(ConstructionCreateUpdateModel model, RequestUser currentUser);
        Task<Response<ConstructionViewModel>> Delete(Guid id, RequestUser currentUser);
        Task<Response<ConstructionViewModel>> GetById(Guid id, RequestUser currentUser);
        Task<Response<ConstructionViewModel>> Update(Guid id, ConstructionCreateUpdateModel model, RequestUser currentUser);
        Task<Response<Pagination<ConstructionViewModel>>> GetPage(ConstructionQueryModel query);
        Task<Response<Pagination<ExecutionTeamsViewModel>>> GetExecutionTeamsInConstruction(ExecutionTeamsQueryModel query);
        Task<Response<string>> ExportListToExcel(ConstructionQueryModel query, RequestUser currentUser);
        Task<Response<List<ConstructionViewModel>>> Import(string path,  RequestUser currentUser);
        #endregion
        
        #region Dashboard Analyze
        // Thống kê theo tiêu chí
        Task<Response<ConstructionAnalyzeAll>> ConstructionAnalyzeAllDashboard(ConstructionQueryModel query);
        // Thống kê theo từng dự án
        Task<Response<ConstructionDashboardViewModel>> DashboardConstruction(Guid constructionId);
        // Thống kê tất cả dự án theo trạng thái
        Task<Response<List<ConstructionChartByStatus>>> ChartConstructionByStatus(ConstructionQueryModel query);
        // Thống kê tỷ lệ vật tư có trong dự án 
        Task<Response<List<ConstructionChartByStatus>>> ChartPercentMaterialInConstruction(ConstructionQueryModel query);
        // Thống kê yêu cầu vật tư cần xử lý
        Task<Response<List<ConstructionChartByStatus>>> ChartMaterialRequestInConstruction(ConstructionQueryModel query);
        // Thống kê yêu cầu tạm ứng cần xử lý
        Task<Response<List<ConstructionChartByStatus>>> ChartAdvanceRequestInConstruction(ConstructionQueryModel query);
        // Tổng hợp thu chi theo dự án
        Task<Response<ConstructionRevenueChart>> SummaryCashbookTransactionByConstruction(ConstructionQueryModel query);
        // Số lượng vật tư thực tế và kế hoạch
        Task<Response<MaterialPlanChart>> SummaryProductByConstruction(ConstructionQueryModel query);
        // Top giá trị 
        Task<Response<List<TopAmountAnalyze>>> TopVatAmountAnalyze(ConstructionQueryModel query);
        // Top khách hàng chủ đầu tư theo công nợ
        Task<Response<List<TopAmountAnalyze>>> TopCashbookTransactionAnalyze(ConstructionQueryModel query);
        #endregion
        Task<Response<ConstructionViewModel>> UpdateTemplateStageIsDone(Guid constructionId, Guid templateStageId);
    }
}

