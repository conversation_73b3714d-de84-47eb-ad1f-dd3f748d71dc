﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.WorkItem;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.WorkItem
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/task")]
    [ApiExplorerSettings(GroupName = "Quản lý công việc dự án")]
    [Authorize]
    [AuthorizeByToken]
    public class TaskController : ControllerBase
    {
        private readonly ITaskHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public TaskController(ITaskHandler handler)
        {
            _handler = handler;
        }


        /// <summary>
        /// Thêm mới công việc dự án
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("CONSTRUCTION", "ADDTASK")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] TaskCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật công việc dự án
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [RightValidate("CONSTRUCTION", "UPDATETASK")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] TaskCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 công việc dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("CONSTRUCTION", "VIEWALL")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách công việc dự án
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("CONSTRUCTION", "VIEWALL")]
        [ProducesResponseType(typeof(ResponsePagination<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<TaskQueryModel>(filter);

            //sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 công việc dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa nhiều công việc dự án
        /// </summary>
        /// <param name="ids">Danh sách Id công việc cần xóa</param>
        /// <returns></returns>
        [HttpDelete, Route("many")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMany([FromQuery] List<Guid> ids)
        {
            var result = await _handler.DeleteMany(ids);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật trạng thái công việc dự án
        /// </summary>
        /// <param name="id">Id công việc</param>
        /// <param name="status">Trạng thái mới (tên enum, ví dụ: "InProgress")</param>
        /// <param name="description">Mô tả cập nhật trạng thái</param>
        /// <returns></returns>
        [HttpPut("{id}/status/{status}")]
        [RightValidate("CONSTRUCTION", "SENDAPPROVALTASK")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatus(Guid id, string status, [FromBody] RejectTaskModel model)
        {
            var result = await _handler.UpdateStatus(id, status, model?.Description);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách công việc dự án theo ConstructionId
        /// </summary>
        /// <param name="constructionId">Id công trình/dự án</param>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet("by-construction/{constructionId}")]
        [RightValidate("CONSTRUCTION", "VIEWALL")]
        [ProducesResponseType(typeof(ResponsePagination<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByConstructionId(
            Guid constructionId,
            [FromQuery] int size = 20,
            [FromQuery] int page = 1,
            [FromQuery] string filter = "{}",
            [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<TaskQueryModel>(filter);
            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort ?? filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPageByConstructionId(constructionId, filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật trạng thái cho nhiều công việc dự án
        /// </summary>
        /// <param name="model">Model chứa danh sách Id công việc, trạng thái mới và mô tả</param>
        /// <returns></returns>
        [HttpPut("status-many")]
        [ProducesResponseType(typeof(Response<List<TaskViewModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatusMany([FromBody] UpdateStatusManyModel model)
        {
            var result = await _handler.UpdateStatusMany(model.Ids, model.Status, model.Description);
            return Helper.TransformData(result);
        }
    }
}
