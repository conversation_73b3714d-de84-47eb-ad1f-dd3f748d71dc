﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.WorkItem;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.WorkItem
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/task")]
    [ApiExplorerSettings(GroupName = "Quản lý công việc dự án")]
    [Authorize]
    [AuthorizeByToken]
    public class TaskController : ControllerBase
    {
        private readonly ITaskHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public TaskController(ITaskHandler handler)
        {
            _handler = handler;
        }


        /// <summary>
        /// Thêm mới công việc dự án
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] TaskCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật công việc dự án
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] TaskCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 công việc dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách công việc dự án
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<TaskQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 công việc dự án
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa nhiều công việc dự án
        /// </summary>
        /// <param name="ids">Danh sách Id công việc cần xóa</param>
        /// <returns></returns>
        [HttpDelete("many")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMany([FromBody] List<Guid> ids)
        {
            var result = await _handler.DeleteMany(ids);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật trạng thái công việc dự án
        /// </summary>
        /// <param name="id">Id công việc</param>
        /// <param name="status">Trạng thái mới (tên enum, ví dụ: "InProgress")</param>
        /// <returns></returns>
        [HttpPut("{id}/status/{status}")]
        [ProducesResponseType(typeof(Response<TaskViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateStatus(Guid id, string status)
        {
            var result = await _handler.UpdateStatus(id, status);
            return Helper.TransformData(result);
        }
    }
}
