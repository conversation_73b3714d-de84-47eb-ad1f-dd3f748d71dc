using NSPC.Business.Services.ConstructionActitvityLog;
using NSPC.Business.Services.ExecutionTeams;
using NSPC.Business.Services.InventoryNote;
using NSPC.Business.Services.ProjectTemplate;
using NSPC.Common;
using NSPC.Data;

namespace NSPC.Business.Services
{
    public class ConstructionCreateUpdateModel
    {
        /// <summary>
        /// Mã công trình/dự án
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Tên công trình dự án
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Loại cấp điện áp
        /// </summary>
        public string VoltageTypeCode { get; set; }

        /// <summary>
        /// Loại chủ đầu tư
        /// </summary>
        public string OwnerTypeCode { get; set; }

        /// <summary>
        /// Id Chủ đầu tư / BQLDA
        /// </summary>
        public Guid InvestorId { get; set; }

        /// <summary>
        /// Template dự án
        /// </summary>
        public Guid ConstructionTemplateId { get; set; }

        /// <summary>
        /// Ngày giao A
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// Độ ưu tiên
        /// </summary>
        public string PriorityCode { get; set; }

        /// <summary>
        /// Tên độ ưu tiên
        /// </summary>
        public string PriorityName { get; set; }

        /// <summary>
        /// Tiến độ hoàn thành theo chủ đầu tư
        /// </summary>
        public string CompletionByInvestor { get; set; }

        /// <summary>
        /// Tiến độ hoàn thành theo XNTV
        /// </summary>
        public string CompletionByCompany { get; set; }

        /// <summary>
        /// Tình hình thực hiện
        /// </summary>
        public string ExecutionStatusCode { get; set; }

        public List<ExecutionTeamsCreateModel> ExecutionTeams { get; set; } = new List<ExecutionTeamsCreateModel>();

        /// <summary>
        /// Tên tình hình thực hiện
        /// </summary>
        public string ExecutionStatusName { get; set; }

        /// <summary>
        /// Tình hình hồ sơ
        /// </summary>
        public string DocumentStatusCode { get; set; }

        /// <summary>
        /// Tên tình hình hồ sơ
        /// </summary>
        public string DocumentStatusName { get; set; }

        /// <summary>
        /// Trạng thái công trình/dự án
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// Tên trạng thái công trình/dự án
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }
    }

    public class ConstructionViewModel
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Mã công trình/dự án
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Tên công trình dự án
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Loại cấp điện áp
        /// </summary>
        public CodeTypeListModel Voltage { get; set; }

        /// <summary>
        /// Loại chủ đầu tư
        /// </summary>
        public string OwnerTypeCode { get; set; }

        /// <summary>
        /// Template dự án
        /// </summary>
        public Guid ConstructionTemplateId { get; set; }

        /// <summary>
        /// Ngày giao A
        /// </summary>
        public DateTime? DeliveryDate { get; set; }

        /// <summary>
        /// Độ ưu tiên
        /// </summary>
        public string PriorityCode { get; set; }

        /// <summary>
        /// Tên độ ưu tiên
        /// </summary>
        public string PriorityName { get; set; }

        /// <summary>
        /// Tiến độ hoàn thành theo chủ đầu tư
        /// </summary>
        public string CompletionByInvestor { get; set; }

        /// <summary>
        /// Tiến độ hoàn thành theo XNTV
        /// </summary>
        public string CompletionByCompany { get; set; }

        /// <summary>
        /// Tình hình thực hiện
        /// </summary>
        public string ExecutionStatusCode { get; set; }

        /// <summary>
        /// Tên tình hình thực hiện
        /// </summary>
        public string ExecutionStatusName { get; set; }

        /// <summary>
        /// Tình hình hồ sơ
        /// </summary>
        public string DocumentStatusCode { get; set; }

        /// <summary>
        /// Tên tình hình hồ sơ
        /// </summary>
        public string DocumentStatusName { get; set; }

        /// <summary>
        /// Trạng thái công trình/dự án
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// Tên trạng thái công trình/dự án
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        public Guid InvestorId { get; set; }

        /// <summary>
        /// Nhân sự tham gia
        /// </summary>
        public string ExecutionParticipantTeams { get; set; }

        /// <summary>
        /// Người theo dõi
        /// </summary>
        public string ExecutionFollowerTeams { get; set; }

        /// <summary>
        /// Tổ thực hiện
        /// </summary>
        public string ExecutionProjectTeams { get; set; }

        public ProjectTemplateViewModel ProjectTemplate { get; set; }
        public List<ExecutionTeamsViewModel> ExecutionTeams { get; set; }
        public List<ConstructionActivityLogViewModel> ActivityLogs { get; set; }
        public List<IssueManagementDTO> IssueManagements { get; set; }

        public Boolean IsHasIssue { get; set; } = false;
        public InvestorDTOViewModel Investor { get; set; }

        public Guid CreatedByUserId { get; set; }
        public Guid? LastModifiedByUserId { get; set; }
        public DateTime? LastModifiedOnDate { get; set; }
        public DateTime CreatedOnDate { get; set; }
        public string CreatedByUserName { get; set; }
        public string LastModifiedByUserName { get; set; }

        /// <summary>
        /// Danh sách giai đoạn của template dự án
        /// </summary>
        public List<jsonb_TemplateStage> TemplateStages { get; set; }
    }

    public class InvestorDTOViewModel
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public InvestorTypeDTO InvestorType { get; set; }
    }

    public class InvestorTypeDTO
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
    }

    public class IssueManagementDTO
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public UserModel User { get; set; }
        public string Status { get; set; }
        public string Description { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public string PriorityLevel { get; set; }
        public string Content { get; set; }
        public Guid CreatedByUserId { get; set; }
        public Guid? LastModifiedByUserId { get; set; }
        public DateTime? LastModifiedOnDate { get; set; }
        public DateTime CreatedOnDate { get; set; }
        public string CreatedByUserName { get; set; }
        public string LastModifiedByUserName { get; set; }
        public List<AttachmentViewModel> Attachments { get; set; }
        public List<AttachmentViewModel> AttachmentsResolve { get; set; }
        public string ReasonReopen { get; set; }
        public string ReasonCancel { get; set; }
        public string ContentResolve { get; set; }
    }

    /// <summary>
    /// Contract DTO
    /// </summary>
    public class ContractDTO
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Mã hợp đồng/phụ lục
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Mã tài liệu
        /// </summary>
        public string DocumentTypeCode { get; set; }

        /// <summary>
        /// Tên tài liệu
        /// </summary>
        public string DocumentTypeName { get; set; }

        /// <summary>
        /// Màu tag loại tài liệu
        /// </summary>
        public string DocumentTypeColor { get; set; }

        /// <summary>
        /// Ngày bắt đầu
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Ngày kết thúc
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// % Thuế GTGT
        /// </summary>
        public decimal VatPercent { get; set; } = 0M;

        /// <summary>
        /// Tổng tiền
        /// </summary>
        public decimal TotalAmount { get; set; } = 0M;

        /// <summary>
        /// Hạng mục thi công
        /// </summary>
        public string ConstructionCategory { get; set; }

        /// <summary>
        /// Tổng đã thu
        /// </summary>
        public decimal TotalReceiptAmount { get; set; } = 0M;

        /// <summary>
        /// Còn lại
        /// </summary>
        public decimal RemainingAmount { get; set; } = 0M;

        /// <summary>
        /// Tổng đã chi
        /// </summary>
        public decimal TotalPaymentAmount { get; set; } = 0M;

        /// <summary>
        /// Lãi/lỗ
        /// </summary>
        public decimal ProfitLossAmount { get; set; } = 0M;

        /// <summary>
        /// Mã trạng thái
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// Tên trạng thái
        /// </summary>
        public string StatusName { get; set; }
    }

    /// <summary>
    /// CashbookTransaction DTO
    /// </summary>
    public class CashbookTransactionDTO
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public decimal Amount { get; set; }
        public Guid EntityId { get; set; }
        public string EntityCode { get; set; }
        public string EntityName { get; set; }
        public string EntityTypeCode { get; set; }
        public DateTime ReceiptDate { get; set; }
        public string EntityTypeName { get; set; }
        public string TransactionTypeCode { get; set; }
        public string PurposeName { get; set; }
        public string IsActive { get; set; }
        public DateTime CreatedOnDate { get; set; }
    }

    /// <summary>
    /// SalesOrder DTO
    /// </summary>
    public class PurchaseOrderDTO
    {
        public Guid Id { get; set; }
        public string OrderCode { get; set; }
        public string StatusCode { get; set; }

        public string SupplierId { get; set; }

        public string SupplierName { get; set; }

        /// <summary>
        /// Mã trạng thái thanh toán
        /// </summary>
        public string PaymentStatusCode { get; set; }

        /// <summary>
        /// Trạng thái xuất kho
        /// </summary>
        public string ImportStatusCode { get; set; }

        /// <summary>
        /// Tổng thành tiền khách phải trả
        /// </summary>
        public decimal Total { get; set; }

        public List<PurchaseOrderItemDTO> Items { get; set; }

        public DateTime? LastModifiedOnDate { get; set; }
        public DateTime CreatedOnDate { get; set; }
    }

    public class PurchaseOrderItemDTO
    {
        /// <summary>
        /// Số dòng
        /// </summary>
        public int LineNo { get; set; }

        /// <summary>
        /// Id Product
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// Mã sản phẩm
        /// </summary>
        public string ProductCode { get; set; }

        /// <summary>
        /// Tên sản phẩm
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// Đơn giá sản phẩm
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// Đơn vị tính
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// Số lượng
        /// </summary>
        public decimal Quantity { get; set; }
    }

    /// <summary>
    /// InventoryNote DTO
    /// </summary>
    public class InventoryNoteDTO
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public Guid? EntityId { get; set; } // ID đối tượng: Khách hàng || Nhà cung cấp

        public string EntityCode { get; set; }

        public string EntityName { get; set; }

        public string EntityTypeCode { get; set; } // Mã loại đối tượng: value ---> customer || supplier

        public string EntityTypeName { get; set; } // Tên loại đối tượng: value ---> Khách hàng || Nhà cung cấp

        public string StatusCode { get; set; } // Mã trạng thái: value ---> DRAFT, COMPLETED, CANCELLED

        public string StatusName { get; set; } // Tên trạng thái: value ---> Nháp, Hoàn thành, Đã hủy

        public DateTime TransactionDate { get; set; }

        public DateTime CreatedOnDate { get; set; }

        /// <summary>
        /// Loại phiếu
        /// </summary>
        public string TransactionTypeName { get; set; }

        public List<InventoryNoteItemViewModel> InventoryNoteItems { get; set; }
    }

    public class MaterialRequestItemDTO
    {
        public string Code { get; set; }

        public string Name { get; set; }

        public string Unit { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal ImportVATPercent { get; set; }

        /// <summary>
        /// Id Product
        /// </summary>
        public Guid ProductId { get; set; }

        public decimal RequestQuantity { get; set; }

        public decimal PlannedQuantity { get; set; }

        /// <summary>
        /// Id yêu cầu vật tư
        /// </summary>
        public Guid MaterialRequestId { get; set; }

        /// <summary>
        /// Id công trình
        /// </summary>
        public Guid ConstructionId { get; set; }
    }

    /// <summary>
    /// Material Request DTO
    /// </summary>
    public class MaterialRequestDTO
    {
        public Guid Id { get; set; }

        /// <summary>
        /// Mã yêu cầu
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Nội dung
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// Hạn xử lý
        /// </summary>
        public DateTime DateProcess { get; set; }

        /// <summary>
        /// Độ ưu tiên
        /// </summary>
        public string Priority { get; set; }

        /// <summary>
        /// Tên độ ưu tiên
        /// </summary>
        public string PriorityName { get; set; }

        /// <summary>
        /// Trạng thái
        /// </summary>
        public string StatusCode { get; set; }

        /// <summary>
        /// Tên trạng thái
        /// </summary>
        public string StatusName { get; set; }

        /// <summary>
        /// Ghi chú
        /// </summary>
        public string Note { get; set; }

        /// <summary>
        /// Id công trình/dự án
        /// </summary>
        public Guid ConstructionId { get; set; }

        public string ConstructionName { get; set; }

        public List<MaterialRequestItemDTO> MaterialRequestItems { get; set; }
        public string CreatedByUserName { get; set; }
        public DateTime CreatedOnDate { get; set; }
    }

    /// <summary>
    /// Advance Request DTO
    /// </summary>
    public class AdvanceRequestDTO
    {
        public Guid Id { get; set; }

        public string Code { get; set; }

        public string Content { get; set; }

        public string PriorityLevelCode { get; set; }

        public string PriorityLevelName { get; set; }

        public string PriorityLevelColor { get; set; }

        public Guid ConstructionId { get; set; }

        public DateTime DueDate { get; set; }

        public string Note { get; set; }

        public string StatusCode { get; set; }

        public string StatusName { get; set; }

        public string StatusColor { get; set; }
        public string ConstructionName { get; set; }

        public string CreatedByUserName { get; set; }
    }

    public class ConstructionDashboardViewModel
    {
        public object AmountData { get; set; }
        public object ContractData { get; set; }
        public object MaterialData { get; set; }
        public object QuantityOfMaterialData { get; set; }
        public object AdvanceData { get; set; }
    }

    public class ConstructionChartByStatus
    {
        public decimal Value { get; set; } = 0M; // Số lượng công trình dự án
        public string Name { get; set; } // Tên trạng thái / Tên vật tư trong dự án;
    }

    public class ConstructionRevenueChart
    {
        public List<string> ListNameOfConstruction { get; set; } = new List<string>();
        public List<decimal> ListReceiptAmountInConstruction { get; set; } = new List<decimal>();
        public List<decimal> ListPaymentAmountInConstruction { get; set; } = new List<decimal>();
    }

    public class MaterialPlanChart
    {
        public List<string> ListNameOfMaterial { get; set; } = new List<string>();
        public List<decimal> ListActualQuantityMaterial { get; set; } = new List<decimal>();
        public List<decimal> ListPlannedQuantityMaterial { get; set; } = new List<decimal>();
    }

    public class TopAmountAnalyze
    {
        public string Title { get; set; }
        public decimal Amount { get; set; } = 0M;
    }

    public class ConstructionAnalyzeAll
    {
        public decimal TotalVatAmount { get; set; } = 0M;
        public decimal TotalRemainingAmount { get; set; } = 0M;
        public decimal TotalReceiptAmount { get; set; } = 0M;
        public decimal TotalPaymentAmount { get; set; } = 0M;
        public decimal TotalProfitLoss { get; set; } = 0M;
    }

    public class ConstructionQueryModel : PaginationRequest
    {
        public DateTime?[] DateRange { get; set; }
        public string StatusCode { get; set; }
        public string ExecutionStatusCode { get; set; }
        public string DocumentStatusCode { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public Guid? InvestorId { get; set; }
    }
}