using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetType;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-type")]
[ApiExplorerSettings(GroupName = "Quản lý loại tài sản")]
[Authorize]
public class AssetTypeController
{
    private readonly IAssetTypeHandler _assetTypeHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetTypeHandler"></param>
    public AssetTypeController(IAssetTypeHandler assetTypeHandler)
    {
        _assetTypeHandler = assetTypeHandler;
    }

    /// <summary>
    /// Tạo loại tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Response<AssetTypeViewModel>>> Create([FromBody] AssetTypeCreateUpdateModel model)
    {
        var result = await _assetTypeHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật loại tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetTypeViewModel>>> Update(Guid id,
        [FromBody] AssetTypeCreateUpdateModel model)
    {
        var result = await _assetTypeHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách loại tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetTypeViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetTypeQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetTypeHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 loại tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetTypeViewModel>>> GetById(Guid id)
    {
        var result = await _assetTypeHandler.GetById(id);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 loại tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _assetTypeHandler.Delete(id);
        return Helper.TransformData(result);
    }
}