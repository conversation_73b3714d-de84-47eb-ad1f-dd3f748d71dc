using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.Asset;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset")]
[ApiExplorerSettings(GroupName = "Quản lý tài sản")]
public class AssetController
{
    private readonly IAssetHandler _assetHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetHandler"></param>
    public AssetController(IAssetHandler assetHandler)
    {
        _assetHandler = assetHandler;
    }

    /// <summary>
    /// Tạo tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> Create([FromBody] AssetCreateUpdateModel model)
    {
        var result = await _assetHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> Update(Guid id, [FromBody] AssetCreateUpdateModel model)
    {
        var result = await _assetHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    public async Task<ActionResult<Response<Pagination<AssetViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetDetailViewModel>>> GetById(Guid id)
    {
        var result = await _assetHandler.GetById(id);

        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy số lượng tài sản theo từng trạng thái
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    [HttpGet, Route("count-by-status")]
    [Authorize]
    public async Task<ActionResult<Response<Dictionary<string, int>>>> CountByStatus([FromQuery] int size = 20,
        [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
    {
        var filterObject = JsonConvert.DeserializeObject<AssetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetHandler.CountByStatus(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    [Authorize]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _assetHandler.Delete(id);

        return Helper.TransformData(result);
    }

    /// <summary>
    /// Báo hỏng tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin báo hỏng</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/report-damage")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> ReportDamage(
        Guid assetId,
        [FromBody] AssetReportDamageModel model)
    {
        var result = await _assetHandler.ReportDamage(assetId, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Báo mất tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin báo mất</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/report-lost")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> ReportLost(
        Guid assetId,
        [FromBody] AssetReportLostModel model)
    {
        var result = await _assetHandler.ReportLost(assetId, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Báo huỷ tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin báo huỷ</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/report-destroyed")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> ReportDestroyed(
        Guid assetId,
        [FromBody] AssetReportDestroyedModel model)
    {
        var result = await _assetHandler.ReportDestroyed(assetId, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cấp phát tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin cấp phát</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/allocate")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> Allocate(
        Guid assetId,
        [FromBody] AssetAllocateModel model)
    {
        var result = await _assetHandler.Allocate(assetId, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Thu hồi tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin thu hồi</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/revoke")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> Revoke(
        Guid assetId,
        [FromBody] AssetAllocateModel model)
    {
        var result = await _assetHandler.Revoke(assetId, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Điều chuyển tài sản
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin điều chuyển</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/transfer")]
    [Authorize]
    public async Task<ActionResult<Response<AssetViewModel>>> Transfer(
        Guid assetId,
        [FromBody] AssetAllocateModel model)
    {
        var result = await _assetHandler.Transfer(assetId, model);
        return Helper.TransformData(result);
    }
    /// <summary>
    /// Cấp phát tài sản qua email
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin phê duyệt</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/allocate-email")]
    public async Task<ActionResult<Response>> AllocateEmail(
        Guid assetId,
        [FromBody] AssetApprovalModel model)
    {
        var result = await _assetHandler.AllocateEmail(assetId, model);
        return Helper.TransformData(result);
    }
    /// <summary>
    /// Thu hồi tài sản qua email
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin phê duyệt</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/revoke-email")]
    public async Task<ActionResult<Response>> RevokeEmail(
        Guid assetId,
        [FromBody] AssetApprovalModel model)
    {
        var result = await _assetHandler.RevokeEmail(assetId, model);
        return Helper.TransformData(result);
    }
    /// <summary>
    /// Điều chuyển tài sản qua email
    /// </summary>
    /// <param name="assetId">ID tài sản</param>
    /// <param name="model">Thông tin phê duyệt</param>
    /// <returns></returns>
    [HttpPut]
    [Route("{assetId:guid}/transfer-email")]
    public async Task<ActionResult<Response>> transferEmail(
        Guid assetId,
        [FromBody] AssetApprovalModel model)
    {
        var result = await _assetHandler.TransferEmail(assetId, model);
        return Helper.TransformData(result);
    }
}