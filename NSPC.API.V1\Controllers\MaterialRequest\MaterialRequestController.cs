using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Business.Services;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.MaterialRequest
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/material-request")]
    [ApiExplorerSettings(GroupName = "Quản lý yêu cầu vật tư")]
    public class MaterialRequestController : Controller
    {
        private readonly IMaterialRequestHandler _handler;

        public MaterialRequestController(IMaterialRequestHandler handler)
        {
            _handler = handler;
        }
        
        /// <summary>
        /// Tạo yêu cầu vật tư
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] MaterialRequestCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật yêu cầu vật tư
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<ConstructionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] MaterialRequestCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách yêu cầu vật tư
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<MaterialRequestQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Chi tiết yêu cầu vật tư
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.GetById(id, user);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Duyệt yêu cầu vật tư
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("approve-material-request/{id}")]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ApproveMaterialRequest(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.ApproveMaterialRequest(id, user);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Gửi duyệt yêu cầu vật tư
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("request-approve-material-request/{id}")]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RequestApproveMaterialRequest(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.RequestApproveMaterialRequest(id, user);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Từ chối duyệt yêu cầu vật tư
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("reject-approve-material-request/{id}")]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RejectApproveMaterialRequest(Guid id,  [FromBody] MaterialRejectReason model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.RejectApproveMaterialRequest(id, model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa yêu cầu vật tư
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<MaterialRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Delete(id, user);
            return Helper.TransformData(result);
        }
    }
}

