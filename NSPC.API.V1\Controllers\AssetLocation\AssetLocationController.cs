using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetLocation;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-location")]
[ApiExplorerSettings(GroupName = "Quản lý vị trí tài sản")]
[Authorize]
public class AssetLocationController
{
    private readonly IAssetLocationHandler _assetLocationHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetLocationHandler"></param>
    public AssetLocationController(IAssetLocationHandler assetLocationHandler)
    {
        _assetLocationHandler = assetLocationHandler;
    }

    /// <summary>
    /// Tạo vị trí tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Response<AssetLocationViewModel>>> Create(
        [FromBody] AssetLocationCreateUpdateModel model)
    {
        var result = await _assetLocationHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật vị trí tài sản
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetLocationViewModel>>> Update(Guid id,
        [FromBody] AssetLocationCreateUpdateModel model)
    {
        var result = await _assetLocationHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách vị trí tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetLocationViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetLocationQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetLocationHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 vị trí tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetLocationViewModel>>> GetById(Guid id)
    {
        var result = await _assetLocationHandler.GetById(id);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 vị trí tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _assetLocationHandler.Delete(id);
        return Helper.TransformData(result);
    }
}