{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    // Remove unused log
    "Filter": [
      {
        "Name": "ByExcluding",
        "Args": {
          "expression": "StartsWith(SourceContext, 'Microsoft.')"
        }
      },
      {
        "Name": "ByExcluding",
        "Args": {
          "expression": "StartsWith(SourceContext, 'API.BasicAuth.Middlewares.CustomAuthHandler')"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithExceptionDetails",
      "WithEnvironmentUserName",
      "WithMachineName",
      "WithProcessId",
      "WithProcessName",
      "WithThreadId"
    ],
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "/mnt/opt/sale-manager/logs/sale-manager.api.log-.txt",
          "rollingInterval": "Day"
        }
      },
      {
        "Name": "Console",
        "Theme": "Code"
      }
    ]
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "PostgreSQLDatabase": "Host=*************;Port=35432;Database=EVN;Username=postgres;Password=********"
    //"PostgreSQLDatabase": "Host=127.0.0.1;Port=5432;Database=Geneat_Admin;Username=postgres;Password=******"
  },
  "Authentication": {
    "Jwt": {
      "Enable": "true",
      "Cookie": "true",
      "Key": "DEV_TEST_OPEN_DATA_DATE_08_03_2024",
      "Anonymous": "SCALE_PROD_ATL_27_06_21",
      "Issuer": "GENEAT CORP",
      "TimeToLive": "99999",
      "TimeToLiveIfNotRemember": "60"
    },
    "Facebook": {
      "AppId": "1004009513735958",
      "AppSecret": "4cc22db8d7e6d4bede8e34619ef340fb"
    },
    "Google": {
      //PRODUCTION
      //"ClientId": "1027957143194-7772rg9nthesnjqkdd9qmthci5s5iffd.apps.googleusercontent.com",
      //"ClientSecret": "gOBVA1yJ4HsMpjkw2k2JSHtE"

      //STAGING
      //"ClientId": "1052433980537-pkn9vhdphsou9uvstsg7vfb9qrl8hhul.apps.googleusercontent.com",
      //"ClientSecret": "N1SylzzKgHwBr6QvDbRqHpue",
      "ClientId": "458714151950-fv7bp4u656lk3bel41agq6jh2j4b27pb.apps.googleusercontent.com",
      "ClientSecret": "458714151950-fv7bp4u656lk3bel41agq6jh2j4b27pb"
    },
    "LinkedIn": {
      "ClientId": "78ulhmkoutz5j5",
      "ClientSecret": "smntBsQCoRYHVEUI"
    },
    "TokenSettings": {
      "Enable": "false"
    },
    "Domain": "geneat.io.vn"
  },
  "EmailConfiguration": {
    "Enable": "true",
    "SSL": "1",
    "SmtpServer": "smtp.gmail.com", //"mail.geneat.vn",
    "SmtpPort": 587,
    //"SmtpUsername": "<EMAIL>",
    //"SmtpPassword": "usmgmvpaovyfrejy",
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "mtgn dnkc hiar ibgw",
    "SenderName": ""
  },
  // Tìm file template excel ở máy local hoặc server
  "StaticFiles": {
    "Folder": "/mnt/opt/xntv-npsc/files/",  // Production
    "Host": "http://localhost:4125/files", // Production
    //"Folder": "C:/opt/geneat/files/",   // Local
    //"Host": "http://localhost/UploadImagesLocal/",  // Local
    "Request": "/files"
  },
  "Syncfusion": {
    "LicenseKey": "Ngo9BigBOggjHTQxAR8/V1NMaF5cXmBCf1FpRmJGdld5fUVHYVZUTXxaS00DNHVRdkdmWX5fc3ZXRWNfVkx2V0A="
  },
  "ChatGPT": {
    "Token": "Bearer 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",
    "ChatCompletionsUrl": "https://openrouter.ai/api/v1/chat/completions",
    "Model": "google/gemini-2.0-pro-exp-02-05:free"
  }
}
