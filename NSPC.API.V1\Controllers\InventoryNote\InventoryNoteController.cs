﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Business.Services.DebtTransaction;
using NSPC.Business.Services.InventoryNote;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.InventoryNote
{
    /// <summary>
    /// Controller quản lý phiếu nhập/xuất
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Authorize, Route("api/v{api-version:apiVersion}/inventory-notes")]
    [ApiExplorerSettings(GroupName = "Quản lý phiếu nhập/xuất")]
    public class InventoryNoteController : ControllerBase
    {
        private readonly IInventoryNoteHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public InventoryNoteController(IInventoryNoteHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Create inventory note record
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("INVENTORYIMPORT" , RightActionConstants.ADD)]
        [RightValidate("INVENTORYEXPORT" , RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<InventoryNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] InventoryNoteCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.Create(model, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Update inventory note record
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [RightValidate("INVENTORYIMPORT" , RightActionConstants.UPDATE)]
        [RightValidate("INVENTORYEXPORT" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<InventoryNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] InventoryNoteCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);

            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Get list inventory note by filter
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidateGroup("INVENTORYIMPORT.VIEW", "INVENTORYEXPORT.VIEW")]
        [ProducesResponseType(typeof(ResponsePagination<InventoryNoteQueryModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<InventoryNoteQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Get inventory note by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidateGroup("INVENTORYIMPORT.VIEW", "INVENTORYEXPORT.VIEW")]
        // [RightValidate("INVENTORYEXPORT" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<InventoryNoteViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Delete inventory notes by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize, HttpDelete]
        [RightValidate("INVENTORYIMPORT" , RightActionConstants.DELETE)]
        [RightValidate("INVENTORYEXPORT" , RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(ResponseDelete), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMultipleAsync([FromQuery] List<Guid> ids)
        {
            var result = await _handler.DeleteMultipleAsync(ids);
            
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cancel inventory notes by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("cancel-multiple")]
        [RightValidate("INVENTORYIMPORT" , RightActionConstants.UPDATE)]
        [RightValidate("INVENTORYEXPORT" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> CancelMultipleAsync([FromQuery] List<Guid> ids)
        {
            var result = await _handler.CancelMultipleAsync(ids);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Import/Export inventory transaction by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("inventory-transaction")]
        [RightValidate("INVENTORYIMPORT" , RightActionConstants.UPDATE)]
        [RightValidate("INVENTORYEXPORT" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> InventoryTransactionAsync([FromQuery] List<Guid> ids)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.InventoryTransactionAsync(ids, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Export excel list inventory note by type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        /// [EnableCors("AllowOrigin")]
        [HttpPost("export-excel-list/{type}")]
        [Authorize]
        [ProducesResponseType(typeof(Response<List<InventoryNoteItemCreateUpdateModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportExcelList(string type)
        {
            var result = _handler.ExportExcelList(type).Result.Data;
            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }
            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result));
        }

        /// <summary>
        /// Export excel list inventory note by type and query parameters for current page
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("export-excel-list-current-page/{type}")]
        [Authorize]
        [ProducesResponseType(typeof(Response<List<InventoryNoteItemCreateUpdateModel>>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportExcelListCurrentPage(string type, [FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<InventoryNoteQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportExcelListCurrentPage(type, filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }

    }
}
