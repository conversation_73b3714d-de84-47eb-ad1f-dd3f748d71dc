﻿using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using Serilog;
using System;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using MongoDB.Driver.Linq;
using NSPC.Business.Services.CashbookTransaction;
using NSPC.Data.Data.Entity.CashbookTransaction;
using static NSPC.Common.Helper;
using NSPC.Data.Data.Entity.InventoryNote;
using NSPC.Business.Services.DebtTransaction;
using NSPC.Data.Data.Entity.Contract;
using Microsoft.AspNetCore.Mvc;
using NPSC.Data;
using NSPC.Business.Services.ConstructionActitvityLog;
using NSPC.Business.Services.Contract;

namespace NSPC.Business.Services
{
    public class IssueManagementHandler : IIssueManagementHandler
    {
       
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        private readonly IAttachmentHandler _attachmentHandler;
        private readonly IIssueActivityLogHandler _issueActivityLogHandler;
        private readonly IConstructionActivityLogHandler _constructionActivityLogHandler;

        public IssueManagementHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper, IAttachmentHandler attachmentHandler, IIssueActivityLogHandler issueActivityLogHandler,  IConstructionActivityLogHandler constructionActivityLogHandler)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
            _attachmentHandler = attachmentHandler;
            _issueActivityLogHandler = issueActivityLogHandler;
            _constructionActivityLogHandler = constructionActivityLogHandler;
        }

        /// <summary>
        /// Tạo mới vướng mắc
        /// </summary>
        /// <param name="model"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<IssueManagementViewModel>> Create(IssueManagementCreateUpdateModel model, RequestUser currentUser)
        {
            try
            {
                var entity = _mapper.Map<sm_IssueManagement>(model);

                if (model.Code == null)
                {
                    entity.Code = await _autoGeneratedIssueCode();
                } ;

                entity.Id = Guid.NewGuid(); 
                entity.PriorityLevel = model.PriorityLevel;
                entity.Status = StatusIssue.WAIT_PROCESSING;
                entity.ConstructionId = model.ConstructionId;            
                entity.CreatedByUserId = currentUser.UserId;
                entity.CreatedByUserName = currentUser.FullName;
                entity.CreatedOnDate = DateTime.Now;
                entity.ExpiryDate = model.ExpiryDate;


                _dbContext.sm_IssueManagement.Add(entity);
                
                var createResult = await _dbContext.SaveChangesAsync();

                if (createResult > 0)
                {
                    #region Log lại hoạt động tạo vướng mắc vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã tạo vướng mắc",
                        CodeLinkDescription = $"{entity.Code}",
                        OrderId =  entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion

                    #region Log lại hoạt động thêm mới vướng mắc vào bảng sm_IssueActivityLog
                    
                    await _issueActivityLogHandler.Create(new IssueActivityLogCreateModel()
                    {
                        Description = "đã tạo thông tin vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion
                }

                return Helper.CreateSuccessResponse(_mapper.Map<IssueManagementViewModel>(entity), "Thêm mới thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<IssueManagementViewModel>(ex);
            }
        }

        /// <summary>
        /// Chi tiết vướng mắc
        /// </summary>
        /// <param name="id"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<IssueManagementViewModel>> GetById(Guid id, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_IssueManagement.Include(x => x.sm_Construction).Include(s => s.Idm_User).ThenInclude(q => q.mk_ChucVu).FirstOrDefaultAsync(x => x.Id == id);
                

                if (entity == null)
                {
                    return Helper.CreateBadRequestResponse<IssueManagementViewModel>("Không tìm thấy bản ghi");
                }

                return Helper.CreateSuccessResponse<IssueManagementViewModel>(_mapper.Map<IssueManagementViewModel>(entity));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<IssueManagementViewModel>(ex);
            }
        }


        public async Task<Response<IssueCountByStatus>>CountByStatus(RequestUser currentUser)
        {
            var cancelStatus = _dbContext.sm_IssueManagement.Where(x => x.Status == StatusIssue.CANCELED).AsNoTracking().Count();
            var resolveStatus = _dbContext.sm_IssueManagement.Where(x => x.Status == StatusIssue.COMPLETED).AsNoTracking().Count();
            var waitResolveStatus = _dbContext.sm_IssueManagement.Where(x => x.Status == StatusIssue.WAIT_PROCESSING).AsNoTracking().Count();
            var totalIssue = _dbContext.sm_IssueManagement.AsNoTracking().Count();

            var result = new IssueCountByStatus
            {
                TotalIssue = totalIssue,
                CancelIssue = cancelStatus,
                ResolveIssue = resolveStatus,
                WaitResolveIssue = waitResolveStatus,
            };

            return Helper.CreateSuccessResponse(result);
        }
        /// <summary>
        /// lấy lịch sử 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<Pagination<IssueActivityLogViewModel>>> GetActivityLogById(Guid id, RequestUser currentUser)
        {
            try
            {
                var query = new IssueActivityLogQuery { };
                var entity =  _dbContext.sm_IssueActivityLog.Where(x => x.OrderId == id).AsNoTracking();
                var data = await entity.GetPageAsync(query);

                var result = _mapper.Map<Pagination<IssueActivityLogViewModel>>(data);
               return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<IssueActivityLogViewModel>>(ex);
            }
        }

        /// <summary>
        /// Cập nhật công trình/dự án
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <param name="currentUser"></param>
        /// <returns></returns>
        public async Task<Response<IssueManagementViewModel>> Update(Guid id, IssueManagementCreateUpdateModel model,
            RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_IssueManagement
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (entity == null)
                {
                    return Helper.CreateBadRequestResponse<IssueManagementViewModel>("Không tìm thấy bản ghi");
                }

                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.FullName;
                entity.LastModifiedOnDate = DateTime.Now;
                entity.PriorityLevel = model.PriorityLevel;
                entity.Description = model.Description;
                entity.ConstructionId = model.ConstructionId;
                entity.Attachments = _mapper.Map<List<jsonb_Attachment>>(model.Attachments);
                entity.Content = model.Content;
                entity.UserId = model.UserId;
                entity.ExpiryDate = model.ExpiryDate;

                _dbContext.sm_IssueManagement.Update(entity);
                
                var result = await _dbContext.SaveChangesAsync();


                if (result > 0)
                {
                    #region Log lại hoạt động cập nhật thông tin vướng mắc vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã cập nhật thông tin vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion

                    //#region Log lại hoạt động chỉnh sửa vướng mắc vào bảng sm_IssueActivityLog
                    //AddActivityLog(entity, "đã chỉnh sửa vướng mắc", currentUser);
                    //#endregion
                    await _issueActivityLogHandler.Create(new IssueActivityLogCreateModel()
                    {
                        Description = "đã cập nhật thông tin vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                       ConstructionId = entity.ConstructionId,
                    }, currentUser);
                }

                return Helper.CreateSuccessResponse<IssueManagementViewModel>(_mapper.Map<IssueManagementViewModel>(entity), "Cập nhật thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<IssueManagementViewModel>(ex);
            }
        }

        public async Task<Response> DeactiveIssueAsync(Guid id, string reasonCancel)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);           
               
                var entity = await _dbContext.sm_IssueManagement.Where(x => x.Id == id).FirstOrDefaultAsync();                 
                 if (entity == null )
                   return Helper.CreateNotFoundResponse("Không tìm thấy vướng mắc");

                 if (entity.Status == StatusIssue.CANCELED)
                   return Helper.CreateBadRequestResponse("Không được hủy vướng mắc có trạng thái “Đã hủy”");

                entity.Status = StatusIssue.CANCELED;
                entity.ReasonCancel = reasonCancel;
                var createResult = await _dbContext.SaveChangesAsync();
                
                if (createResult > 0)
                {
                    #region Log lại hoạt động huỷ vướng mắc vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã huỷ vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId =  entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion

                    #region Log lại hoạt động xử lý vướng mắc vào bảng sm_IssueManagement
                  
                    await _issueActivityLogHandler.Create(new IssueActivityLogCreateModel()
                    {
                        Description = "đã huỷ vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion
                }

                return Helper.CreateSuccessResponse(string.Format("Hủy vướng mắc thành công."));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, ByUserIds: {@requestUserId}");
                return Utils.CreateExceptionResponseError(ex);
            }
        }
        public async Task<Response> ResolveIssueAsync(Guid id, ResolveModel model)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                var entity = await _dbContext.sm_IssueManagement.Where(x => x.Id == id).FirstOrDefaultAsync();
                if (entity == null)
                    return Helper.CreateNotFoundResponse("Không tìm thấy vướng mắc");
                if (entity.Status == StatusIssue.CANCELED)
                    return Helper.CreateNotFoundResponse("Không được xử lý vướng mắc “Đã huỷ” ");

                entity.ContentResolve =  model.ContentResolve;
                entity.AttachmentsResolve = _mapper.Map<List<jsonb_Attachment>>(model.AttachmentsResolve);
                entity.Status = StatusIssue.COMPLETED;
                var createResult = await _dbContext.SaveChangesAsync();


                if (createResult > 0)
                {
                    #region Log lại hoạt động mở lại vướng mắc vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã mở lại vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion

                    await _issueActivityLogHandler.Create(new IssueActivityLogCreateModel()
                    {
                        Description = "đã xử lý thông tin vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);

                }


                return Helper.CreateSuccessResponse(string.Format("Xử lý vướng mắc thành công."));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, ByUserIds: {@requestUserId}");
                return Utils.CreateExceptionResponseError(ex);
            }
        }
        public async Task<Response> ReopenIssueAsync(Guid id, string reasonOpen)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                var entity = await _dbContext.sm_IssueManagement.Where(x => x.Id == id).FirstOrDefaultAsync();
                if (entity == null)
                    return Helper.CreateNotFoundResponse("Không tìm thấy vướng mắc");
                if (reasonOpen == null)
                    return Helper.CreateNotFoundResponse("Vui lòng nhập lý do mở lại");

                if (entity.Status == StatusIssue.WAIT_PROCESSING)
                    return Helper.CreateBadRequestResponse("Không mở lại vướng mắc có trạng thái “Chờ xừ lý”");               
                entity.Status = StatusIssue.WAIT_PROCESSING;   
                entity.ReasonReopen = reasonOpen;
                var createResult = await _dbContext.SaveChangesAsync();
                
                
                if (createResult > 0)
                {
                    #region Log lại hoạt động mở lại vướng mắc vào bảng sm_ConstructionActivityLog
                    await _constructionActivityLogHandler.Create(new ConstructionActivityLogCreateModel()
                    {
                        Description = "đã mở lại vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId =  entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);
                    #endregion

                    await _issueActivityLogHandler.Create(new IssueActivityLogCreateModel()
                    {
                        Description = "đã mở lại vướng mắc",
                        CodeLinkDescription = $"{entity.Code} - {entity.Content}",
                        OrderId = entity.Id,
                        ConstructionId = entity.ConstructionId,
                    }, currentUser);

                }
                

                return Helper.CreateSuccessResponse(string.Format("Mở lại vướng mắc thành công."));
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, ByUserIds: {@requestUserId}");
                return Utils.CreateExceptionResponseError(ex);
            }
        }

        /// <summary>
        /// Lấy ra tồn kho của sản phẩm
        /// </summary>
        /// <param name="id"></param>
        /// <param name="wareCode"></param>
        /// <returns></returns>
        public async Task<Response<List<ConstructionItemViewModel>>> GetByConstructionId(Guid constructionId)
        {
            try
            {
                var entityList = await _dbContext.sm_ConstructionItems.Where(x => x.ConstructionId == constructionId).ToListAsync();

                var result = entityList.GroupBy(x => new { x.ProductId, x.Code, x.Name, x.Unit, x.ConstructionId }).Select(
                    x => new ConstructionItemViewModel()
                    {
                        ProductId = x.Key.ProductId,
                        Code = x.Key.Code,
                        Name = x.Key.Name,
                        Unit = x.Key.Unit,
                        ConstructionId = x.Key.ConstructionId,
                        PlannedQuantity = x.Where(x => x.ConstructionId == constructionId).Sum(x => x.PlannedQuantity),
                    });

                return new Response<List<ConstructionItemViewModel>>(_mapper.Map<List<ConstructionItemViewModel>>(result));

            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<List<ConstructionItemViewModel>>(ex);
            }
        }

      public async Task<Response<Pagination<IssueManagementViewModel>>> GetPage(IssueManagementQuery query)
        {
            try
            {
                var predicate = BuildQuery(query);
                var queryResult = _dbContext.sm_IssueManagement.Include(x => x.Idm_User).ThenInclude(x => x.mk_ChucVu).Include(x => x.sm_Construction).AsNoTracking().Where(predicate);                                 

                var data = await queryResult.GetPageAsync(query);
               

                var result = _mapper.Map<Pagination<IssueManagementViewModel>>(data);
                

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<Pagination<IssueManagementViewModel>>(ex);
            }
        }

        private Expression<Func<sm_IssueManagement, bool>> BuildQuery(IssueManagementQuery query)
        {
            var predicate = PredicateBuilder.New<sm_IssueManagement>(true);

            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Content.ToLower().Contains(query.FullTextSearch.ToLower()) || s.CreatedByUserName.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (query.Status != null && query.Status.Count() > 0)
            {
                predicate.And(x => query.Status.Contains(x.Status));
            }

            if (query.DateRange != null && query.DateRange.Count() > 0)
            {
                if (query.DateRange[0].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date >= query.DateRange[0].Value.Date);

                if (query.DateRange[1].HasValue)
                    predicate.And(x => x.CreatedOnDate.Date <= query.DateRange[1].Value.Date);
            }
            if (query.ExpiryDate != null && query.ExpiryDate.Count() > 0)
            {
                if (query.ExpiryDate[0].HasValue)
                    predicate.And(x => x.ExpiryDate.Value.Date >= query.ExpiryDate[0].Value.Date);

                if (query.ExpiryDate[1].HasValue)
                    predicate.And(x => x.ExpiryDate.Value.Date <= query.ExpiryDate[0].Value.Date);
            }
            if (!string.IsNullOrEmpty(query.PriorityLevel))
            {
                predicate.And(x => query.PriorityLevel.Contains(x.PriorityLevel));
            }

            if (query.ConstructionId.HasValue)
            {
                predicate.And(s => s.ConstructionId == query.ConstructionId);
            }
            if (query.CreatedByUserId.HasValue)
            {
                predicate.And(s => s.CreatedByUserId == query.CreatedByUserId);
            }
            if (query.UserId.HasValue)
            {
                predicate.And(s => s.UserId == query.UserId);
            }

            return predicate;
        }
        public async Task<Response<ConstructionDashboardViewModel>> DashboardConstruction(Guid constructionId)
        {
            try
            {
                var contracts = await _dbContext.sm_Contract.Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.ImplementationStatus }).Select(x => new
                    {
                        x.Key.ImplementationStatus,
                        TotalCount = x.Count()
                    })
                    .ToListAsync();
                var allStatuses = new List<ImplementationStatus>
                {
                    ImplementationStatus.PendingApproval,
                    ImplementationStatus.NotImplemented,
                    ImplementationStatus.Approved,
                    ImplementationStatus.InProgress,
                    ImplementationStatus.OnHoldOrSuspended,
                };

                var contractData = allStatuses.Select(status => new
                {
                    statusName = status,
                    totalCount = contracts.FirstOrDefault(c => c.ImplementationStatus == status)?.TotalCount ?? 0
                }).ToList();

                var topAdvanceRequest = await _dbContext.sm_AdvanceRequest
                    .Where(x => x.ConstructionId == constructionId)
                    .OrderByDescending(x => x.TotalAmount)
                    .Select(x => new
                    {
                        x.Code,
                        x.TotalLineAmount
                    })
                    .ToListAsync();
                var materialRequest = await _dbContext.sm_MaterialRequestItem
                    .Include(x => x.sm_Product)
                    .Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.sm_Product.Name })
                    .Select(x => new
                    {
                        ProductName = x.Key.Name,
                        TotalCount = x.Count()
                    }).ToListAsync();
                var quantityMaterialRequest = await _dbContext.sm_MaterialRequestItem
                    .Include(x => x.sm_Product)
                    .Where(x => x.ConstructionId == constructionId)
                    .GroupBy(x => new { x.sm_Product.Name, x.RequestQuantity, x.PlannedQuantity })
                    .Select(x => new
                    {
                        ProductName = x.Key.Name,
                        x.Key.RequestQuantity,
                        x.Key.PlannedQuantity,
                    }).ToListAsync();
                var result = new ConstructionDashboardViewModel()
                {
                    ContractData = contractData,
                    AdvanceData = topAdvanceRequest,
                    MaterialData = materialRequest,
                    QuantityOfMaterialData = quantityMaterialRequest

                };
                return CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return CreateExceptionResponse<ConstructionDashboardViewModel>(ex);
            }
        }
        public async Task<Response<IssueManagementViewModel>> Delete(Guid id, RequestUser currentUser)
        {
            try
            {
                var entity = await _dbContext.sm_IssueManagement
                    .FirstOrDefaultAsync(x => x.Id == id);

                if (entity == null)
                {
                    return Helper.CreateBadRequestResponse<IssueManagementViewModel>("Không tìm thấy bản ghi");
                }

                if(entity.Status == StatusIssue.COMPLETED)
                {
                    return Helper.CreateBadRequestResponse<IssueManagementViewModel>("Không thể xoá vướng mắc “Đã xừ lý”");
                }

                _dbContext.sm_IssueManagement.Remove(entity);
                await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse<IssueManagementViewModel>
                    (_mapper.Map<IssueManagementViewModel>(entity), "Xoá thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                return Helper.CreateExceptionResponse<IssueManagementViewModel>(ex);
            }
        }
        private async Task<string> _autoGeneratedIssueCode()
        {
            try
            {
                        

                var code = IssueManagementConstants.IssueCodePrefix + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_IssueManagement.AsNoTracking().Where(s => s.Code.Contains(code)).OrderByDescending(x => x.CreatedOnDate).FirstOrDefaultAsync();


                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("", ex);
                return string.Empty;
            }
        }

    }
}

