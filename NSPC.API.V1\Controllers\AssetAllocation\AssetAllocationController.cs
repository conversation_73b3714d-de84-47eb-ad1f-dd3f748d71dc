using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Business.Services.AssetAllocation;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetAllocation;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-allocation")]
[ApiExplorerSettings(GroupName = "Quản lý phân bổ tài sản")]
public class AssetAllocationController
{
    private readonly IAssetAllocationHandler _assetAllocationHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetAllocationHandler"></param>
    public AssetAllocationController(IAssetAllocationHandler assetAllocationHandler)
    {
        _assetAllocationHandler = assetAllocationHandler;
    }

    /// <summary>
    /// L<PERSON>y danh sách phân bổ tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    [Authorize]
    public async Task<ActionResult<Response<Pagination<AssetAllocationViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetAllocationQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetAllocationHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 tài sản được phẩn bổ
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetAllocationViewModel>>> GetById(Guid id)
    {
        var result = await _assetAllocationHandler.GetById(id);
        return Helper.TransformData(result);
    }
}