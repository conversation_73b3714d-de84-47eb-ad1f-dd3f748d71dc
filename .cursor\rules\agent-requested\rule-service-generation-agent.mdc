---
description: When a request is made to create service layer components (handler, interface, models) for an entity.
globs:
alwaysApply: false
---
# Service Layer Generation Guidelines

## Context

- **When to apply:** When a request is made to create service layer components (handler, interface, models) for an entity.
- **Prerequisites:**
    - A `.Business` project exists.
    - An `SMDbContext` and helper classes/files (`Helper.cs`, `RequestData.cs` for `Response<T>`, `ResponseData.cs` for `Pagination<T>`, `PredicateBuilder`) are available.
    - AutoMapper is set up with an `AutoMapperConfig.cs`.
    - A logging mechanism (e.g., static `Log` or injected `ILogger`) is in place.
- **Why needed:** To ensure consistency and adherence to project standards when creating service layer components for entities.

## Critical Rules

1.  **Service-Entity Correspondence:** Service name generally matches the entity name (e.g., `sm_Asset` entity corresponds to `Asset` service).
2.  **File Location (`.Business` project):
    *   Create files in the `.Business` project, within a `Services` folder.
    *   Inside `Services/`, create a subfolder named after the service (e.g., `Services/Asset/`).
    *   Place all service-related files (`Handler.cs`, `IHandler.cs`, `Models.cs`) within this subfolder.
3.  **File Structure (per service):
    *   `{ServiceName}Handler.cs` (e.g., `AssetHandler.cs`)
    *   `I{ServiceName}Handler.cs` (e.g., `IAssetHandler.cs`)
    *   `{ServiceName}Models.cs` (e.g., `AssetModels.cs`)
4.  **Models File (`{ServiceName}Models.cs`):
    *   Contains model classes used by the handler.
    *   **`{EntityName}ViewModel`**: View model for client responses. Enum fields from the entity become `string` type in the ViewModel. (e.g., `AssetViewModel`). Typically shared by GetPage and GetById unless a specific `DetailViewModel` is requested.
    *   **`{EntityName}CreateUpdateModel`**: Contains fields for creating or updating an entity. Enum fields from the entity become `string` type. (e.g., `AssetCreateUpdateModel`). If create and update operations have different fields, split into `{EntityName}CreateModel` and `{EntityName}UpdateModel`.
    *   **`{EntityName}QueryModel`**: Contains fields for search and pagination. Must inherit from `PaginationRequest`. Enum fields from the entity become `string` type. (e.g., `AssetQueryModel : PaginationRequest`).
5.  **Interface File (`I{ServiceName}Handler.cs`):
    *   Defines methods for the handler, corresponding to API endpoints.
    *   Method return types are typically wrapped in `Response<T>` (from `RequestData.cs`).
    *   Standard CRUD methods:
        *   `Create`: `Task<Response<ViewModel>> Create({EntityName}CreateUpdateModel model)`
        *   `Update`: `Task<Response<ViewModel>> Update(Guid id, {EntityName}CreateUpdateModel model)`
        *   `GetPage`: `Task<Response<Pagination<ViewModel>>> GetPage({EntityName}QueryModel query)` (using `Pagination<T>` from `ResponseData.cs`)
        *   `GetById`: `Task<Response<ViewModelOrDetailViewModel>> GetById(Guid id)`
        *   `Delete`: `Task<Response> Delete(Guid id)`
6.  **Handler File (`{ServiceName}Handler.cs`):
    *   Implements the corresponding `I{ServiceName}Handler` interface.
    *   **Response Handling**: Use `Helper` class (from `Helper.cs`) for creating responses. Messages in responses must be in Vietnamese.
        *   Success example: `Helper.CreateSuccessResponse(_mapper.Map<AssetViewModel>(asset), "Cập nhật tài sản thành công")`
        *   Error example: `Helper.CreateErrorResponse<AssetViewModel>("Lỗi không xác định")` or `Helper.CreateExceptionResponse<AssetViewModel>(e)`
    *   **Error Handling & Logging**: Wrap all public method implementations in `try...catch` blocks. Log exceptions with relevant context (parameters).
        ```csharp
        public async Task<Response<AssetViewModel>> Update(Guid id, AssetCreateUpdateModel model)
        {
            try
            {
                // ... business logic ...
            }
            catch (Exception e)
            {
                Log.Error(e, "Error updating asset with id {AssetId}", id);
                Log.Information("Params: Id: {Id}, Model: {@Model}", id, model);
                return Helper.CreateExceptionResponse<AssetViewModel>(e, "Có lỗi xảy ra trong quá trình cập nhật.");
            }
        }
        ```
    *   **Dependencies**: Inject `SMDbContext` for database operations and `IHttpContextAccessor` for accessing HTTP context information.
    *   **Request User**: Use `Helper.GetRequestInfo(_httpContextAccessor)` to get current user details; do not pass `RequestUser` objects as method parameters.
    *   **ID Validation**: For methods accepting an entity ID, validate the entity's existence before proceeding.
    *   **`ValidateCreateUpdateModel` Method**: Implement a private async method `Task<Response<T>> ValidateCreateUpdateModel<T>({EntityName}CreateUpdateModel model, Guid? entityId = null)` for validating input models. This method should return a `Response<T>` object if validation fails, or `null` if validation succeeds. It must validate `string` representations of enums from the `CreateUpdateModel`.
    *   **`BuildQuery` Method**: Implement a private method `Expression<Func<sm_EntityName, bool>> BuildQuery({EntityName}QueryModel query)` to construct query expressions for `GetPage` or similar methods.
        *   Always include a `TenantId` filter based on the current user if applicable: `predicate.And(x => x.TenantId == currentUser.TenantId);`
        *   Query fields are optional; check for `null` or whitespace before adding to the predicate.
        *   Text searches should be case-insensitive (e.g., using `.ToLower().Contains()`).
        *   For enum fields in `QueryModel` (which are strings), parse and validate the string against the actual enum values before adding to the predicate.
        ```csharp
        private Expression<Func<sm_Asset, bool>> BuildQuery(AssetQueryModel query)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
            var predicate = PredicateBuilder.New<sm_Asset>(true);

            if (currentUser.TenantId != null)
            {
                predicate = predicate.And(x => x.TenantId == currentUser.TenantId);
            }
            if (!string.IsNullOrWhiteSpace(query.FullTextSearch))
            {
                var searchTerm = query.FullTextSearch.Trim().ToLower();
                predicate = predicate.And(x => x.Name.ToLower().Contains(searchTerm) || x.Code.ToLower().Contains(searchTerm));
            }
            // ... other filters, including enum checks ...
            if (!string.IsNullOrWhiteSpace(query.StatusString) && Enum.TryParse<AssetStatus>(query.StatusString, true, out var statusEnum))
            {
                predicate = predicate.And(x => x.Status == statusEnum);
            }
            return predicate;
        }
        ```
    *   Refer to existing handlers like `VehicleRequestHandler` or `AssetHandler` for structural examples.
7.  **AutoMapper Configuration**:
    *   In the `.Business` project, create a new AutoMapper profile class (e.g., `AssetProfile.cs`) in the `AutoMapper/Profiles/` folder. This class must inherit from `Profile`.
    *   Define mappings:
        *   Entity to ViewModel: `CreateMap<sm_Asset, AssetViewModel>().ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));`
        *   CreateUpdateModel to Entity: `CreateMap<AssetCreateUpdateModel, sm_Asset>().ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse<AssetStatus>(src.Status, true)));` (Ensure string enum values are validated in `ValidateCreateUpdateModel` before mapping).
    *   AutoMapper handles fields with the same name and type automatically.
    *   Register the new profile class in `AutoMapperConfig.cs` by adding it to the `MapperConfiguration` (e.g., `cfg.AddProfile<AssetProfile>();`).
8.  **Dependency Injection (`Program.cs` in API project)**:
    *   Register the service and its interface (e.g., `services.AddScoped<IAssetHandler, AssetHandler>();`).
    *   Add a comment before the registration: `// Asset Service`. 

## Examples

<example>
  Suppose we are creating a service for an entity `sm_Widget` with a `WidgetStatus` enum.

  **1. File Structure (`.Business` project):**
  ```
  Services/
      Widget/
          IWidgetHandler.cs
          WidgetHandler.cs
          WidgetModels.cs
  AutoMapper/
      Profiles/
          WidgetProfile.cs
  ```

  **2. `WidgetModels.cs`:**
  ```csharp
  // Assuming PaginationRequest exists
  public class WidgetViewModel 
  {
      public Guid Id { get; set; }
      public string Name { get; set; }
      public string Status { get; set; } // Enum as string
      public DateTime CreatedOnDate { get; set; }
  }

  public class WidgetCreateUpdateModel
  {
      [Required] public string Name { get; set; }
      [Required] public string Status { get; set; } // Enum as string
  }

  public class WidgetQueryModel : PaginationRequest
  {
      public string Name { get; set; }
      public string Status { get; set; } // Enum as string for querying
  }
  ```

  **3. `IWidgetHandler.cs`:**
  ```csharp
  // Assuming Response<T> and Pagination<T> exist
  public interface IWidgetHandler
  {
      Task<Response<WidgetViewModel>> Create(WidgetCreateUpdateModel model);
      Task<Response<WidgetViewModel>> Update(Guid id, WidgetCreateUpdateModel model);
      Task<Response<Pagination<WidgetViewModel>>> GetPage(WidgetQueryModel query);
      Task<Response<WidgetViewModel>> GetById(Guid id);
      Task<Response> Delete(Guid id);
  }
  ```

  **4. `WidgetHandler.cs` (Partial Example - Create Method & BuildQuery):**
  ```csharp
  public class WidgetHandler : IWidgetHandler
  {
      private readonly SMDbContext _context;
      private readonly IMapper _mapper;
      private readonly IHttpContextAccessor _httpContextAccessor;
      // Assume Log is available (e.g., static Serilog Log or injected ILogger)

      public WidgetHandler(SMDbContext context, IMapper mapper, IHttpContextAccessor httpContextAccessor)
      {
          _context = context;
          _mapper = mapper;
          _httpContextAccessor = httpContextAccessor;
      }

      public async Task<Response<WidgetViewModel>> Create(WidgetCreateUpdateModel model)
      {
          try
          {
              var validationResponse = await ValidateCreateUpdateModel<WidgetViewModel>(model);
              if (validationResponse != null) return validationResponse;

              var widget = _mapper.Map<sm_Widget>(model);
              // ... set other properties like CreatedByUserId from Helper.GetRequestInfo ...
              await _context.sm_Widgets.AddAsync(widget);
              await _context.SaveChangesAsync();
              return Helper.CreateSuccessResponse(_mapper.Map<WidgetViewModel>(widget), "Tạo widget thành công.");
          }
          catch (Exception e)
          {
              Log.Error(e, "Error creating widget.");
              Log.Information("Params: Model: {@Model}", model);
              return Helper.CreateExceptionResponse<WidgetViewModel>(e, "Lỗi khi tạo widget.");
          }
      }
      
      private async Task<Response<T>> ValidateCreateUpdateModel<T>(WidgetCreateUpdateModel model, Guid? entityId = null)
      {
          if (!Enum.TryParse<WidgetStatus>(model.Status, true, out _))
          {
              return Helper.CreateErrorResponse<T>($"Trạng thái '{model.Status}' không hợp lệ.");
          }
          // ... other validations ...
          return null; // Indicates success
      }

      private Expression<Func<sm_Widget, bool>> BuildQuery(WidgetQueryModel query)
      {
          var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
          var predicate = PredicateBuilder.New<sm_Widget>(true);
          if (currentUser.TenantId != null) predicate = predicate.And(x => x.TenantId == currentUser.TenantId);
          if (!string.IsNullOrWhiteSpace(query.Name)) predicate = predicate.And(x => x.Name.ToLower().Contains(query.Name.ToLower().Trim()));
          if (!string.IsNullOrWhiteSpace(query.Status) && Enum.TryParse<WidgetStatus>(query.Status, true, out var statusEnum))
          {
              predicate = predicate.And(x => x.Status == statusEnum);
          }
          return predicate;
      }
      // ... other methods (Update, GetPage, GetById, Delete) ...
  }
  ```

  **5. `WidgetProfile.cs` (in `.Business/AutoMapper/Profiles/`):**
  ```csharp
  public class WidgetProfile : Profile
  {
      public WidgetProfile()
      {
          CreateMap<sm_Widget, WidgetViewModel>()
              .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()));
          CreateMap<WidgetCreateUpdateModel, sm_Widget>()
              .ForMember(dest => dest.Status, opt => opt.MapFrom(src => Enum.Parse<WidgetStatus>(src.Status, true)));
      }
  }
  ```

  **6. `AutoMapperConfig.cs` (in `.Business`):**
  ```csharp
  // ... existing config ...
  cfg.AddProfile<WidgetProfile>();
  // ... existing config ...
  ```

  **7. `Program.cs` (in API Project):**
  ```csharp
  // ... other services ...
  // Widget Service
  builder.Services.AddScoped<IWidgetHandler, WidgetHandler>();
  // ... other services ...
  ```
</example>

<example type="invalid">
  **1. Incorrect File Location:** `WidgetHandler.cs` placed directly in `.Business/Handlers/` instead of `.Business/Services/Widget/`.
  
  **2. Missing `try-catch` in Handler Method:**
  ```csharp
  public async Task<Response<WidgetViewModel>> GetById(Guid id)
  {
      var widget = await _context.sm_Widgets.FindAsync(id);
      if (widget == null) return Helper.CreateErrorResponse<WidgetViewModel>("Không tìm thấy widget.");
      return Helper.CreateSuccessResponse(_mapper.Map<WidgetViewModel>(widget));
      // No try-catch, no logging for potential errors during mapping or DB access.
  }
  ```

  **3. Incorrect Enum Handling in ViewModel:**
  ```csharp
  public class WidgetViewModel 
  {
      public WidgetStatus Status { get; set; } // Should be string
  }
  ```

  **4. Missing DI Registration:** `IWidgetHandler` and `WidgetHandler` not added to `Program.cs`.

  **5. AutoMapper Profile Not Registered:** `WidgetProfile` created but not added in `AutoMapperConfig.cs`.

  **6. `BuildQuery` not handling TenantId or case-insensitivity properly.**
</example>
