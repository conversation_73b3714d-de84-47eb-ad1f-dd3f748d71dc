﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.LichSuChamSoc
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/lich-su-cham-soc")]
    [ApiExplorerSettings(GroupName = "Quản lý lịch sử chăm sóc")]
    public class LichSuChamSocController : ControllerBase
    {
        private readonly ILichSuChamSocHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public LichSuChamSocController(ILichSuChamSocHandler handler)
        {
            _handler = handler;
        }


        /// <summary>
        /// Tạo lịch sử chăm sóc
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] LichSuChamSocCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật lịch sử chăm sóc
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] LichSuChamSocCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Cập nhật lịch sử chăm sóc (log lại trong hệ thống)
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("change-activity/{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChangeActivity(Guid id, [FromBody] LichSuChamSocCreateUpdateModel model)
        {
            var result = await _handler.ChangeActivity(id, model);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Cập nhật công việc hoàn thành
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("confirm-task-completion/{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ConfirmTaskCompletion(Guid id)
        {
            var result = await _handler.ConfirmTaskCompletion(id);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Khôi phục công việc
        /// </summary>
        /// <param name="model"></param>`
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("restore-task/{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RestoreTask(Guid id)
        {
            var result = await _handler.RestoreTask(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 lịch sử chăm sóc
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách lịch sử chăm sóc
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<LichSuChamSocQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 lịch sử chăm sóc
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<LichSuChamSocViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }
    }
}
