---
description: This rule should be applied when a request is made to create a new API controller for a specific service.
globs:
alwaysApply: false
---
# API Controller Generation Rule

## Context

- **When to apply this rule**: This rule should be applied when a request is made to create a new API controller for a specific service.
- **Prerequisites or conditions**: A service (or at least its name) for which the controller is being created should be identified.
- **Why the rule was added or is needed**: To ensure a consistent directory structure for API controllers within the `.API` project and to promote adherence to established coding patterns by referencing existing controllers.

## Critical Rules

- Controller files MUST be created within the project that handles API endpoints (typically named with a `.API` suffix, e.g., `MyProject.API`).
- Within this API project, locate or create a root folder named `Controllers`.
- Inside the `Controllers` folder, create a new subfolder named after the specific service the controller will manage. For example, if creating a controller for an "Asset" service, the folder should be `Asset`.
- The controller class file MUST be named `[ServiceName]Controller.cs` (e.g., `AssetController.cs`) and placed directly inside the service-specific subfolder created in the previous step (e.g., `/.API/Controllers/Asset/AssetController.cs`).
- When implementing the controller logic, refer to existing controllers such as `VehicleRequestController.cs` or `AssetController.cs` as examples for structure, dependency injection, action method patterns, and response types.

## Examples

<example>
  User Request: "Create a controller for the Product service."
  
  Expected Action:
  1. Identify the API project (e.g., `XntvNpsc.API`).
  2. Create the file at the path: `XntvNpsc.API/Controllers/Product/ProductController.cs`.
  3. The content of `ProductController.cs` should be scaffolded based on the patterns observed in `VehicleRequestController.cs` or `AssetController.cs`.
</example>

<example type="invalid">
  User Request: "Add a controller for Inventory."
  
  Incorrect Actions:
  1. Creating the file as `XntvNpsc.API/Controllers/InventoryController.cs` (missing the `Inventory` subfolder).
  2. Creating the file in a different project, like `XntvNpsc.Application/Controllers/Inventory/InventoryController.cs`.
  3. Not referencing `VehicleRequestController.cs` or `AssetController.cs` for the controller's structure and implementation, leading to an inconsistent design.
</example>
