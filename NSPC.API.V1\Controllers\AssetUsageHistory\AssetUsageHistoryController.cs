using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetUsageHistory;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-usage-history")]
[ApiExplorerSettings(GroupName = "Lịch sử sử dụng tài sản")]
[Authorize]
public class AssetUsageHistoryController
{
    private readonly IAssetUsageHistoryHandler _assetUsageHistoryHandler;

    /// <summary>
    /// </summary>
    /// <param name="assetUsageHistoryHandler"></param>
    public AssetUsageHistoryController(IAssetUsageHistoryHandler assetUsageHistoryHandler)
    {
        _assetUsageHistoryHandler = assetUsageHistoryHandler;
    }

    /// <summary>
    /// L<PERSON>y danh sách lịch sử sử dụng tài sản
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetUsageHistoryViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetUsageHistoryQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetUsageHistoryHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 lịch sử sử dụng tài sản
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetUsageHistoryViewModel>>> GetById(Guid id)
    {
        var result = await _assetUsageHistoryHandler.GetById(id);
        return Helper.TransformData(result);
    }
}