using NSPC.Common;
using static NSPC.Common.Helper;

namespace NSPC.Business.Services.ConstructionWeekReport
{
    public interface IConstructionWeekReportHandler
    {
        Task<Response<ConstructionWeekReportViewModel>> Create(ConstructionWeekReportCreateModel model,
            RequestUser currentUser);

        Task<Response<Pagination<ConstructionWeekReportViewModel>>> GetPage(ConstructionWeekReportQueryModel query);
    }
}

