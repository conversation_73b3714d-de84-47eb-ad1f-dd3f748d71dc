using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Business.Services;
using NSPC.Business.Services.CashbookTransaction;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/social-media")]
    [ApiExplorerSettings(GroupName = "Social Media")]
    public class SocialMediaController : ControllerBase
    {
        private readonly ISocialMediaHandler _handler;

        public SocialMediaController(ISocialMediaHandler handler)
        {
            _handler = handler;
        }
        
        #region Social Media Post Process
        /// <summary>
        /// Tạo bài viết mới
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("post")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreatePost([FromBody] SocialMediaPostCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.CreateAsync(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật bài viết
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("post/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdatePost(Guid id, [FromBody] SocialMediaPostCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.UpdateAsync(id, model, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Xóa bài viết
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("delete-post/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeletePost(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.RemoveAsync(id, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Lấy danh sách bài viết
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("list-post")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.VIEWALL)]
        [ProducesResponseType(typeof(ResponsePagination<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPaymentVoucherInConstruction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SocialMediaQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPageAsync(filterObject);
            return Helper.TransformData(result);
        }
        #endregion
        
        #region Comment Process
        /// <summary>
        /// Tạo comment mới cho bài viết
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("comments")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<CommentsViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateComment([FromBody] CommentsCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.CreateCommentsAsync(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật comment trong bài viết
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("comments/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateComment(Guid id, [FromBody] CommentsCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.UpdateCommentsAsync(id, model, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Xóa comment trong bài viết
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("delete-comments/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteComment(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.RemoveCommentsAsync(id, user);

            return Helper.TransformData(result);
        }
        #endregion
        
        #region Comment Items Process
        /// <summary>
        /// Tạo comment con trong comment cha
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("comment-items")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CommentsItemsCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.CreateCommentItemsAsync(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật comment con trong comment cha
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("comment-items/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] CommentsItemsCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.UpdateCommentItemsAsync(id, model, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Xóa comment con trong comment cha
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("delete-comment-items/{id}")]
        [Authorize, RightValidate("CONSTRUCTIONNEWS", RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<SocialMediaPostViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.RemoveCommentItemsAsync(id, user);

            return Helper.TransformData(result);
        }
        #endregion
    }
}
