﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.KhachHang
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/customers")]
    [ApiExplorerSettings(GroupName = "Quản lý khách hàng")]
    public class KhachHangController : ControllerBase
    {
        private readonly IKhachHangHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public KhachHangController(IKhachHangHandler handler)
        {
            _handler = handler;
        }
        
        /// <summary>
        /// Thống kê số lượng khách hàng theo tiêu chí
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("customer-summary")]
        [ProducesResponseType(typeof(Response<CustomerSummaryView>), StatusCodes.Status200OK)]
        
        public async Task<IActionResult> GetSummaryTransaction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<KhachHangQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetCustomerSummary(filterObject);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Tạo khách hàng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] KhachHangCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật khách hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] KhachHangCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Thay đổi mối quan hệ khách hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("change-customer-type/{id}")]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChangeCustomerType(Guid id, [FromBody] KhachHangCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.ChangeCustomerType(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 khách hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("CUSTOMER", RightActionConstants.VIEWALL)]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách khách hàng
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("CUSTOMER", RightActionConstants.VIEWALL)]
        [ProducesResponseType(typeof(ResponsePagination<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<KhachHangQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 khách hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }


        /// <summary>
        /// Xóa nhiều khách hàng
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpDelete, Route("")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteMany([FromQuery] List<Guid> ids)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.DeleteMultiple(ids);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Kiểm tra báo giá khách hàng
        /// </summary>
        /// <param name="request"></param>
        /// <param name="id">Id bản ghi</param>
        /// <returns></returns>
        [Authorize, HttpGet("{id}/quotes")]
        [ProducesResponseType(typeof(Response<KhachHangViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CheckIfCustomerHasQuotes(Guid id)
        {
            var searchResults = await _handler.CheckIfCustomerHasQuotes(id);

            return Helper.TransformData(searchResults);
        }

    }
}
