﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.EInvoice;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.EInvoice
{
    [ApiVersion("1.0")]
    [ApiController]
    [Authorize]
    [Route("api/v{api-version:apiVersion}/e-invoices")]
    [ApiExplorerSettings(GroupName = "Quản lý hóa đơn tạo tay")]
    public class EInvoiceController : ControllerBase
    {
        private readonly IEInvoiceHandler _handler;

        public EInvoiceController(IEInvoiceHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Thêm mới hóa đơn
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAsync([FromBody] EInvoiceCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.CreateAsync(model, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách hóa đơn
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<EInvoiceQueryModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPageAsync([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<EInvoiceQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPageAsync(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xem chi tiết hóa đơn
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByIdAsync(Guid id)
        {
            var result = await _handler.GetByIdAsync(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Chỉnh sửa hóa đơn
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] EInvoiceCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.UpdateAsync(id, model, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa hóa đơn
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(Guid id)
        {
            var result = await _handler.DeleteAsync(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Kích hoạt hóa đơn
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("active/{id}")]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ActiveAsync(Guid id)
        {
            var result = await _handler.ActiveAsync(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Thanh toán hóa đơn
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("payment/{id}")]
        [ProducesResponseType(typeof(Response<EInvoiceViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> PaymentHistoryAsync(Guid id, [FromBody] PaymentHistoryViewModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.PaymentHistoryAsync(id, model, currentUser);

            return Helper.TransformData(result);
        }
    }
}
