﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.ChamCong;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.ChamCong
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/cham-cong")]
    [ApiExplorerSettings(GroupName = "Quản lý bảng chấm công")]
    public class ChamCongController : ControllerBase
    {
        private readonly IChamCongHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public ChamCongController(IChamCongHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo bảng chấm công
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<ChamCongIdCreatedModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] ChamCongCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 bảng chấm công
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<ChamCongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 bảng chấm công
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<ChamCongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách bảng chấm công
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<ChamCongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<ChamCongQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật bảng chấm công
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<ChamCongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] ChamCongCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Kích hoạt bảng chấm công
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("{id}/active")]
        //[Allow(RoleConstants.AdminRoleCode)]
        [ProducesResponseType(typeof(ResponseUpdate), StatusCodes.Status200OK)]
        public async Task<IActionResult> ActiveBangChamCongAsync(Guid id)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            var result = await _handler.ChangeActiveStatusAsync(id, true, requestInfo.UserId);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hủy kích hoạt bảng chấm công
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("{id}/deactive")]
        //[Allow(RoleConstants.AdminRoleCode)]
        [ProducesResponseType(typeof(ResponseUpdate), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeactiveBangChamCongAsync(Guid id)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            var result = await _handler.ChangeActiveStatusAsync(id, false, requestInfo.UserId);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// import từ excel
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("excel/import")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> Import(string path)
        {
            var currentUser = Helper.GetRequestInfo(HttpContext.Request);
            var result = await _handler.Import(path);

            return Helper.TransformData(result);
        }
    }
}
