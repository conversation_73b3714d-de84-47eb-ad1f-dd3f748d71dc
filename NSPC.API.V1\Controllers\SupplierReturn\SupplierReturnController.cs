﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/supplier-return")]
    [ApiExplorerSettings(GroupName = "Quản lý trả hàng nhà cung cấp")]
    public class SupplierReturnController : Controller
    {
        private readonly ISupplierReturnHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public SupplierReturnController(ISupplierReturnHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo phiếu trả hàng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] SupplierReturnCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách phiếu
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(ResponsePagination<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SupplierReturnQueryModel>(filter);

            var user = Helper.GetRequestInfo(Request);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xuất kho trả hàng nhà cung cấp
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("supplier-return-inventory/{id}")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SupplierReturnInventory(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.SupplierReturnOrder(id, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hủy đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("cancelled-return-order/{id}")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CancelledReturnOrder(Guid id)
        {
            var result = await _handler.CancelledReturnedOrder(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hoàn tiền đơn trả hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("refund/{id}")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RefundPaymentOrder(Guid id, [FromBody] RefundSupplierCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.RefundPaymentOrder(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [RightValidate("PURCHASEORDERRETURN" , RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<SupplierReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("export-list-to-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SupplierReturnQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }
}
