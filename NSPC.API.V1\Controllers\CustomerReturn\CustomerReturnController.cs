﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;
using NSPC.Common;

namespace NSPC.API.V1.Controllers
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/customer-return")]
    [ApiExplorerSettings(GroupName = "Quản lý khách trả hàng")]
    public class CustomerReturnController : ControllerBase
    {
        private readonly ICustomerReturnHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public CustomerReturnController(ICustomerReturnHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo phiếu trả hàng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] CustomerReturnCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách phiếu
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(ResponsePagination<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<CustomerReturnQueryModel>(filter);

            var user = Helper.GetRequestInfo(Request);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject, user);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Nhập kho khách trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("customer-return-inventory/{id}")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CustomerReturnInventory(Guid id)
        {
            var user = Helper.GetRequestInfo(Request);
            var result = await _handler.CustomerReturnInventory(id, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hủy đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("cancelled-return-order/{id}")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CancelledReturnOrder(Guid id)
        {
            var result = await _handler.CanceledReturnOrder(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hoàn tiền đơn trả hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("refund/{id}")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RefundPaymentOrder(Guid id, [FromBody] RefundCreateModel model)
        {
            var user = Helper.GetRequestInfo(Request);
            
            var result = await _handler.RefundPaymentOrder(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa đơn trả hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [RightValidate("SALESORDERRETURN" , RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<CustomerReturnViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Export excel to list by query parameters
        /// </summary>
        /// <param name="type"></param>
        /// <param name="query"></param>
        /// <returns></returns>
        [EnableCors("AllowOrigin")]
        [HttpPost("export-list-to-excel")]
        [Authorize]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportListToExcel([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<CustomerReturnQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportListToExcel(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }

            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }
}
