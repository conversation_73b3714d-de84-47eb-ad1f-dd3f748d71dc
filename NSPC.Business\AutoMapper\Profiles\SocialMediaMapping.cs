using AutoMapper;
using NSPC.Data.Entity;
using NSPC.Business.Services;

namespace NSPC.Business.AutoMapper.Profiles
{
    public class SocialMediaMapping: Profile
    {
        public SocialMediaMapping()
        {
            // Comments
            CreateMap<sm_Comments, CommentsViewModel>()
                .ForMember(dest => dest.CommentItems,
                    x => x.MapFrom(src => src.sm_CommentItems.OrderBy(x => x.CreatedOnDate)))
                .ForMember(dest => dest.SocialMediaPost,
                    x => x.MapFrom(src => src.sm_SocialMediaPost));
            CreateMap<CommentsCreateModel, sm_Comments>();
            
            CreateMap<sm_Comments, CommentDTOModel>()
                .ForMember(dest => dest.AvatarUrl,
                    x => x.MapFrom(src => src.CreatedByUser.AvatarUrl))
                .ForMember(dest => dest.CommentItems,
                    x => x.MapFrom(src => src.sm_CommentItems
                        .OrderBy(x => x.CreatedOnDate)));
            
            // Comments Items
            CreateMap<sm_CommentItems, CommentsItemsViewModel>()
                .ForMember(dest => dest.AvatarUrl,
                    x => x.MapFrom(src => src.CreatedByUser.AvatarUrl));
            CreateMap<CommentsItemsCreateModel, sm_CommentItems>();
            
            // Social Media Post
            CreateMap<sm_SocialMediaPost, SocialMediaPostViewModel>()
                .ForMember(dest => dest.Comments,
                    x => x.MapFrom(src => src.sm_Comments
                        .OrderByDescending(x => x.CreatedOnDate)))
                .ForMember(dest => dest.TotalComments,
                    x => x.MapFrom(src => src.sm_Comments.Count
                                          + src.sm_Comments.Sum(x => x.sm_CommentItems.Count )));
            CreateMap<SocialMediaPostCreateModel, sm_SocialMediaPost>();
        }
    }
}

