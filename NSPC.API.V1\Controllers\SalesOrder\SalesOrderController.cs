﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services;

//using NSPC.Business.Services.QuanLyKho;
//using NSPC.Business.Services.QuanLyPhieu;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.SalesOrder
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/sales-orders")]
    [ApiExplorerSettings(GroupName = "Quản lý đơn hàng")]
    public class SalesOrderController : ControllerBase
    {
        private readonly ISalesOrderHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public SalesOrderController(ISalesOrderHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo đơn bán hàng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [RightValidate("SALESORDER" , RightActionConstants.ADD)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] SalesOrderCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.Create(model, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa đơn bán hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.DELETE)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết đơn bán hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật đơn bán hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] SalesOrderCreateUpdateModel model)
        {
            var user = Helper.GetRequestInfo(Request);

            var result = await _handler.Update(id, model, user);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách đơn hàng
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [RightValidate("SALESORDER" , RightActionConstants.VIEW)]
        [ProducesResponseType(typeof(ResponsePagination<SalesOrderQueryModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SalesOrderQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }
        
        /// <summary>
        /// Thống kê số lượng và giá trị đơn hàng theo trạng thái
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("sales-order-summary")]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]

        public async Task<IActionResult> GetSummaryTransaction([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SalesOrderQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetSalesOrderSummary(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hủy đơn hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("reject-order/{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Reject(Guid id)
        {
            var result = await _handler.RejectOrder(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Nhập hàng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("export-order/{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Export(Guid id)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.ExportOrder(id, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Thanh toán đơn bán hàng
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("payment/{id}")]
        [RightValidate("SALESORDER" , RightActionConstants.UPDATE)]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChargePaymentOrder(Guid id, [FromBody] SalesOrderCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.ChargePaymentOrder(id, model, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xuất ra danh sách đơn hàng excel
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("export-excel-list")]
        [Authorize]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportExcelList()
        {
            var result = _handler.ExportExcelList().Result.Data;
            byte[] file;
            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result, FileMode.Open))
                {
                 await fs.CopyToAsync(ms);   
                }
                file = ms.ToArray();
            }
            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result));
        }
        /// <summary>
        /// Xuất ra danh sách đơn hàng excel theo trang
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("export-excel-list-current-page")]
        [Authorize]
        [ProducesResponseType(typeof(Response<SalesOrderViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ExportExcelListCurrentPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<SalesOrderQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.ExportExcelListCurrentPage(filterObject);
            if (result.Data == null)
            {
                return NotFound(new { message = "Không tìm thấy dữ liệu để xuất." });
            }
            byte[] file;

            using (MemoryStream ms = new MemoryStream())
            {
                using (FileStream fs = new FileStream(result.Data, FileMode.Open))
                {
                    await fs.CopyToAsync(ms);
                }
                file = ms.ToArray();
            }

            return File(file, System.Net.Mime.MediaTypeNames.Application.Octet, Path.GetFileName(result.Data));
        }
    }
}
