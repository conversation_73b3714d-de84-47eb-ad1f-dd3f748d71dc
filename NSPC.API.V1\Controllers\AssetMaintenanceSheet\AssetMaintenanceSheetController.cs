using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.AssetMaintenanceSheet;

/// <summary>
/// </summary>
[ApiVersion("1.0")]
[ApiController]
[Route("api/v{api-version:apiVersion}/asset-maintenance")]
[ApiExplorerSettings(GroupName = "Quản lý phiếu bảo trì tài sản")]
[Authorize]
public class AssetMaintenanceSheetController
{
    private readonly IAssetMaintenanceSheetHandler _assetMaintenanceSheetHandler;

    public AssetMaintenanceSheetController(IAssetMaintenanceSheetHandler assetMaintenanceSheetHandler)
    {
        _assetMaintenanceSheetHandler = assetMaintenanceSheetHandler;
    }

    /// <summary>
    /// Tạo phiếu bảo trì
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPost]
    public async Task<ActionResult<Response<AssetMaintenanceSheetViewModel>>> Create(
        [FromBody] AssetMaintenanceSheetCreateUpdateModel model)
    {
        var result = await _assetMaintenanceSheetHandler.Create(model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Cập nhật phiếu bảo trì
    /// </summary>
    /// <param name="model"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetMaintenanceSheetViewModel>>> Update(Guid id,
        [FromBody] AssetMaintenanceSheetCreateUpdateModel model)
    {
        var result = await _assetMaintenanceSheetHandler.Update(id, model);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy danh sách phiếu bảo trì
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <param name="sort"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<ActionResult<Response<Pagination<AssetMaintenanceSheetViewModel>>>> GetPage(
        [FromQuery] int size = 20,
        [FromQuery] int page = 1,
        [FromQuery] string filter = "{}",
        [FromQuery] string sort = ""
    )
    {
        var filterObject = JsonConvert.DeserializeObject<AssetMaintenanceSheetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetMaintenanceSheetHandler.GetPage(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy chi tiết 1 phiếu bảo trì
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet, Route("{id:guid}")]
    public async Task<ActionResult<Response<AssetMaintenanceSheetViewModel>>> GetById(Guid id)
    {
        var result = await _assetMaintenanceSheetHandler.GetById(id);

        return Helper.TransformData(result);
    }

    /// <summary>
    /// Lấy số lượng phiếu bảo trì theo từng trạng thái
    /// </summary>
    /// <param name="size"></param>
    /// <param name="page"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    [HttpGet, Route("count-by-status")]
    public async Task<ActionResult<Response<Dictionary<string, int>>>> CountByStatus([FromQuery] int size = 20,
        [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
    {
        var filterObject = JsonConvert.DeserializeObject<AssetMaintenanceSheetQueryModel>(filter);

        sort = string.IsNullOrEmpty(sort) ? "-CreatedOnDate" : sort;
        filterObject.Sort = sort != null ? sort : filterObject.Sort;
        filterObject.Size = size;
        filterObject.Page = page;

        var result = await _assetMaintenanceSheetHandler.CountByStatus(filterObject);
        return Helper.TransformData(result);
    }

    /// <summary>
    /// Xóa 1 phiếu bảo trì
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete, Route("{id:guid}")]
    public async Task<ActionResult<Response>> Delete(Guid id)
    {
        var result = await _assetMaintenanceSheetHandler.Delete(id);

        return Helper.TransformData(result);
    }

    /// <summary>
    /// Hoàn thành bảo trì
    /// </summary>
    /// <param name="id"></param>
    /// <param name="model"></param>
    /// <returns></returns>
    [HttpPut, Route("{id:guid}/complete")]
    public async Task<ActionResult<Response<AssetMaintenanceSheetViewModel>>> CompleteMaintenance(Guid id,
        AssetMaintenanceSheetCompleteModel model)
    {
        var result = await _assetMaintenanceSheetHandler.CompleteMaintenance(id, model);
        return Helper.TransformData(result);
    }
}