---
description: When a request is made to create a new Entity Framework Core entity class.
globs:
alwaysApply: false
---
# Entity Framework Class Generation Guidelines

## Context

- **When to apply:** When a request is made to create a new Entity Framework Core entity class.
- **Prerequisites:** 
    - The project has a `.Data` project.
    - Entity classes are stored in `NSPC.Data/Data.Entity/`.
    - A base class `BaseTableService<T>` exists.
    - An `SMDbContext.cs` file exists for DbContext updates.
- **Why needed:** To ensure consistency, maintainability, and adherence to project-specific conventions when creating entity classes.

## Critical Rules

1.  **Prefix:** Entities must have a prefix. Default to `sm_` if not specified (e.g., `sm_Asset`).
2.  **Location:** Create entities in the `NSPC.Data` project, under `Data.Entity/`. Inside `Data.Entity/`, create a folder named after the entity (without prefix), and place the class file there (e.g., `NSPC.Data/Data.Entity/Asset/sm_Asset.cs`).
3.  **Naming:** Entity names should be English and singular.
4.  **Table Annotation:** Always include `[Table("prefix_EntityName")]` annotation before the class definition.
5.  **Field Naming:**
    *   Avoid repeating the table name in field names (e.g., use `Id`, `Name` instead of `AssetId`, `AssetName`).
    *   Exception: Foreign key fields explicitly referencing another part of the entity name (e.g., `CategoryId` in `sm_Asset`).
6.  **Field Comments:** Use XML comments (`/// <value>Description</value>`) to describe the purpose of each field.
7.  **Base Class & Common Fields:**
    *   Entities must inherit from `BaseTableService<T>` (e.g., `public class sm_Asset : BaseTableService<sm_Asset>`).
    *   The `BaseTableService<T>` is defined as:
        ```csharp
        public abstract class BaseTableService<T> where T : BaseTableService<T>
        {
            public Guid CreatedByUserId { get; set; }
            public Guid? LastModifiedByUserId { get; set; }
            public DateTime? LastModifiedOnDate { get; set; } = DateTime.Now;
            public DateTime CreatedOnDate { get; set; } = DateTime.Now;
            public string CreatedByUserName { get; set; }
            public string LastModifiedByUserName { get; set; }
            public virtual Guid? TenantId { get; set; }
            [ForeignKey("TenantId")]
            public virtual Idm_Tenant Idm_Tenant { get; set; }
        }
        ```
    *   Add foreign key navigation properties for `CreatedByUserId` and `LastModifiedByUserId` to `idm_User` in the entity class:
        ```csharp
        [ForeignKey(nameof(CreatedByUserId))]
        public virtual idm_User CreatedByUser { get; set; }

        [ForeignKey(nameof(LastModifiedByUserId))]
        public virtual idm_User LastModifiedByUser { get; set; }
        ```
    *   Do not redefine fields already present in `BaseTableService`.
8.  **Constraints:** Use data annotations for field constraints (e.g., `[Required, MaxLength(100)]`).
9.  **Boolean Fields:** Use `bool` type, prefix field names with `Is` (e.g., `public bool IsApproved { get; set; }`).
10. **Date Fields:** Use `DateTime` or `DateTime?` type, suffix field names with `Date` (e.g., `public DateTime TransactionDate { get; set; }`).
11. **Currency Fields:** Use `decimal` type, suffix field names with `Amount` (e.g., `public decimal TotalAmount { get; set; }`).
12. **Count Fields:** Use `int` (or `long` if necessary), suffix field names with `Count` (e.g., `public int ItemCount { get; set; }`).
13. **Total/Aggregated Fields:** Prefix with `Total` for sums/aggregations related to child tables. Data type `int` or `decimal` (e.g., `public int TotalOrderItemCount { get; set; }`).
14. **Percentage Fields:** Use `float` or `double` type, suffix field names with `Percentage` (e.g., `public float DiscountPercentage { get; set; }`).
15. **URL Fields:** Use `string` type, suffix field names with `Url` (e.g., `public string ProfileImageUrl { get; set; }`).
16. **Foreign Keys & Navigation Properties:**
    *   Foreign key field: `RelatedEntityNameId` (e.g., `public Guid AssetTypeId { get; set; }`).
    *   Navigation property: `RelatedEntityName` (e.g., `public virtual sm_AssetType AssetType { get; set; }`).
    *   Include `[ForeignKey(nameof(RelatedEntityNameId))]` on the navigation property.
17. **DbContext Update:**
    *   Add the new entity `DbSet` to `SMDbContext.cs`.
    *   Precede the `DbSet` declaration with a comment indicating the entity name (e.g., `// sm_Asset`).
18. **Flexibility:** These are guidelines. User-specified names or structures take precedence. Refer to existing entities like `sm_VehicleRequest.cs` or `sm_Asset.cs` for further examples.
19. Entity for properties name must be written in English

## Examples

<example>
  ```csharp
  // Correct Entity: NSPC.Data/Data.Entity/Product/sm_Product.cs
  using System;
  using System.ComponentModel.DataAnnotations;
  using System.ComponentModel.DataAnnotations.Schema;
  // Assuming BaseTableService and idm_User are in accessible namespaces

  namespace NSPC.Data.Data.Entity // Fictional namespace
  {
      [Table("sm_Product")]
      public class sm_Product : BaseTableService<sm_Product>
      {
          [Key]
          public Guid Id { get; set; }

          /// <value>Name of the product</value>
          [Required]
          [MaxLength(200)]
          public string Name { get; set; }

          /// <value>Description of the product</value>
          public string Description { get; set; }

          /// <value>Price of the product</value>
          [Column(TypeName = "decimal(18,2)")]
          public decimal PriceAmount { get; set; }

          /// <value>Indicates if the product is currently active</value>
          public bool IsActive { get; set; }

          /// <value>Date the product was launched</value>
          public DateTime? LaunchDate { get; set; }

          /// <value>Category ID for the product</value>
          [Required]
          public Guid CategoryId { get; set; }

          [ForeignKey(nameof(CategoryId))]
          public virtual sm_Category Category { get; set; }
          
          // Foreign keys from BaseTableService
          [ForeignKey(nameof(CreatedByUserId))]
          public virtual idm_User CreatedByUser { get; set; }

          [ForeignKey(nameof(LastModifiedByUserId))]
          public virtual idm_User LastModifiedByUser { get; set; }
      }

      // Dummy sm_Category for example completeness
      [Table("sm_Category")]
      public class sm_Category : BaseTableService<sm_Category> 
      {
          [Key]
          public Guid Id { get; set; }
          public string Name { get; set; }

          // Foreign keys from BaseTableService
          [ForeignKey(nameof(CreatedByUserId))]
          public virtual idm_User CreatedByUser { get; set; }

          [ForeignKey(nameof(LastModifiedByUserId))]
          public virtual idm_User LastModifiedByUser { get; set; }
      }

      // Dummy idm_User for example completeness
      [Table("idm_User")]
      public class idm_User 
      {
          [Key]
          public Guid Id {get; set;}
          public string UserName {get; set;}
      }
      
      // Dummy Idm_Tenant for example completeness
      [Table("Idm_Tenant")]
      public class Idm_Tenant
      {
          [Key]
          public Guid Id { get; set; }
          public string Name { get; set; }
      }
  }

  // In SMDbContext.cs:
  // ...
  // sm_Product
  public DbSet<sm_Product> sm_Products { get; set; }
  // sm_Category
  public DbSet<sm_Category> sm_Categories { get; set; }
  // ...
  ```
</example>

<example type="invalid">
  ```csharp
  // Invalid Entity: MyProduct.cs (Wrong naming, location, missing prefix, inheritance, annotations)
  // Placed directly in project root or wrong folder

  public class MyProduct // No prefix, not inheriting BaseTableService
  {
      public int ProductId { get; set; } // Table name in field, not Guid
      public string ProductName { get; set; } // Table name in field

      // Missing CreatedByUserId, LastModifiedByUserId FKs and other BaseTableService fields
      // Missing XML comments
      // Missing Is* for bool
      public bool Active { get; set; }
      // Missing *Date for DateTime
      public DateTime Introduction { get; set; }
      // Missing *Amount for decimal
      public decimal Cost { get; set; }
      // Missing CategoryId and navigation property
  }

  // In SMDbContext.cs:
  public DbSet<MyProduct> Products { get; set; } // No comment, wrong entity name convention
  ```
</example>
