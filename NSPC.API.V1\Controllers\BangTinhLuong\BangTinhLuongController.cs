﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.BangTinhLuong;
using NSPC.Business.Services.ChamCong;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.BangTinhLuong
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/bang-tinh-luong")]
    [ApiExplorerSettings(GroupName = "Quản lý bảng tính lương")]
    public class BangTinhLuongController : ControllerBase
    {
        private readonly IBangTinhLuongHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public BangTinhLuongController(IBangTinhLuongHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// T<PERSON><PERSON> bảng t<PERSON>h lương
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<BangTinhLuongIdCreatedModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] BangTinhLuongCreateUpdateModel model)
        {
            var result = await _handler.Create(model);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa 1 bảng tính lương
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<BangTinhLuongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _handler.Delete(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy chi tiết 1 bảng tính lương
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<BangTinhLuongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetById(Guid id)
        {
            var result = await _handler.GetById(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách bảng tính lương
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<BangTinhLuongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPage([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<BangTinhLuongQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật bảng tính lương
        /// </summary>
        /// <param name="model"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<BangTinhLuongViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Update(Guid id, [FromBody] BangTinhLuongCreateUpdateModel model)
        {
            var result = await _handler.Update(id, model);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Kích hoạt bảng lương
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("{id}/active")]
        //[Allow(RoleConstants.AdminRoleCode)]
        [ProducesResponseType(typeof(ResponseUpdate), StatusCodes.Status200OK)]
        public async Task<IActionResult> ActiveBangLuongAsync(Guid id)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            var result = await _handler.ChangeActiveStatusAsync(id, true, requestInfo.UserId);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Hủy kích hoạt bảng lương
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Authorize, HttpPut, Route("{id}/deactive")]
        //[Allow(RoleConstants.AdminRoleCode)]
        [ProducesResponseType(typeof(ResponseUpdate), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeactiveBangLuongAsync(Guid id)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            var result = await _handler.ChangeActiveStatusAsync(id, false, requestInfo.UserId);

            // Hander response
            return Helper.TransformData(result);
        }
    }
}
