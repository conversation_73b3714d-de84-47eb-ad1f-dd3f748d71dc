﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.PhongBan;
using NSPC.Business.Services.WorkingDay;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.WorkingDay
{
    /// <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/working-day")]
    [ApiExplorerSettings(GroupName = "Quản lý ngày làm việc")]
    public class WorkingDayController : ControllerBase
    {
        private readonly IWorkingDayHandler _handler;

        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public WorkingDayController(IWorkingDayHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        ///L<PERSON>y danh sách ngày làm việc
        /// </summary>
        /// <param name="year"></param>       
        /// <param name="month"></param>       
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        public async Task<IActionResult> GetWorkingDays(int year, int? month, int? day)
        {
            var result = await _handler.GetWorkingDays(year, month, day);
            return Helper.TransformData(result);
        }

        /// <summary>
        ///Update ngày làm việc hay ngày nghỉ
        /// </summary>
        /// <param name="year"></param>       
        /// <param name="month"></param>       
        /// <param name="day"></param>       
        /// <param name="model"></param>       
        /// <returns></returns>
        [HttpPut, Route("{year}/{month}/{day}")]
        [Authorize, Allow(RoleConstants.AdminRoleCode, RoleConstants.WorkDayFullControlRoleCode)]
        public async Task<IActionResult> UpdateWorkingDay(int year, int month, int day, [FromBody] WorkingDayUpdateModel model)
        {
            var result = await _handler.UpdateWorkingDay(year, month, day, model);
            return Helper.TransformData(result);
        }

        /// <summary>
        ///Bootstrap dữ liệu cho 1 năm
        /// </summary>
        /// <param name="year"></param>                  
        /// <returns></returns>
        [HttpPost]
        [Authorize, Allow(RoleConstants.AdminRoleCode, RoleConstants.WorkDayFullControlRoleCode)]
        [Route("{year}")]
        public async Task<IActionResult> GetWorkingDays(int year)
        {
            var result = await _handler.BootstrapWorkingDays(year);
            return Helper.TransformData(result);
        }

        /// <summary>
        ///Lấy danh sách tổng ngày làm việc
        /// </summary>
        /// <param name="year"></param>       
        /// <returns></returns>
        [HttpGet]
        [Authorize]
        [Route("total-workingday")]
        public async Task<IActionResult> GetTotalWorkingDays(int year)
        {
            var result = await _handler.GetTotalWorkingDay(year);
            return Helper.TransformData(result);
        }
    }
}
