# Active Context

_This document is dynamic and should be updated frequently. It builds upon `productContext.md`, `systemPatterns.md`, and `techContext.md` and informs `progress.md`._

**Last Updated:** YYYY-MM-DD HH:MM UTC (CURSOR_INSERT_CURRENT_TIME_UTC)

## 1. Current Focus / Immediate Goals

*   **Primary Task(s) Under Development:**
    *   **Task 1:** Generation and refinement of service layer components (Models, Handler Interface, Handler Implementation, AutoMapper Profile) for the `sm_Contract` entity.
*   **Objectives for this Session/Week:** Successfully generate a functional service layer for `sm_Contract`, resolving all linter/compilation errors, particularly in `ContractHandler.cs`.

## 2. Recent Changes & Decisions

*   **Key Changes Implemented (Last 1-3 sessions):**
    *   Generated `NSPC.Business/Services/Contract/ContractModels.cs`, `IContractHandler.cs`, `ContractHandler.cs`, and `NSPC.Business/AutoMapper/Profiles/ContractProfile.cs`.
    *   Registered `ContractProfile` in `NSPC.Business/AutoMapper/AutoMapperConfig.cs`.
    *   Addressed various linter errors in these files related to `DbSet` naming, `Pagination` instantiation, `Helper` method usage, and `idm_User` namespace.
    *   Refactored `GetPage` method in `ContractHandler.cs` to use an anticipated `GetPageAsync` extension method and `AsNoTracking()`, aiming for consistency with other handlers like `VehicleRequestHandler`.
    *   Moved validation logic from data annotations in `ContractCreateUpdateModel` (in `ContractModels.cs`) to explicit checks within the `ValidateCreateUpdateModel` method in `ContractHandler.cs`.
    *   User removed `FullTextSearch` property from `ContractQueryModel` in `ContractModels.cs`.
    *   User manually modified `ContractHandler.cs`:
        *   Introduced `LinqKit.CònTrước` (presumably as a replacement for a `PredicateBuilder`).
        *   Removed `using NSPC.Common.Utils;` (which was re-added by AI in an attempt to support `GetPageAsync` and then removed again by user, presumably due to the persistent linter error: "Utils is a type not a namespace"). This removal currently makes the `GetPageAsync` call in the refactored `GetPage` method non-functional.
        *   Performed minor formatting and `using` statement cleanup.
*   **Recent Key Decisions Made:**
    *   Attempted to follow project patterns (e.g., using `GetPageAsync` extension) for service implementation.
    *   Validation for `ContractCreateUpdateModel` is now primarily handled within `ContractHandler.cs` instead of by data annotations.
    *   The use of `NSPC.Common.Utils` namespace remains problematic due to unresolved linter errors, impacting access to common extension methods and utility classes.
*   **Files Recently Modified/Viewed:**
    *   `NSPC.Data/Data/Entity/Contract/sm_Contract.cs`
    *   `NSPC.Business/Services/Contract/ContractModels.cs` (Modified by AI and User)
    *   `NSPC.Business/Services/Contract/IContractHandler.cs`
    *   `NSPC.Business/Services/Contract/ContractHandler.cs` (Modified by AI and User)
    *   `NSPC.Business/AutoMapper/Profiles/ContractProfile.cs`
    *   `NSPC.Business/AutoMapper/AutoMapperConfig.cs`
    *   `NSPC.Business/Services/VehicleRequest/VehicleRequestHandler.cs` (Viewed for pattern reference)
    *   `NSPC.Common/*` (Various files for `Helper`, `Pagination`, `ExtensionMethods`)
    *   `NSPC.Data/Data/SMDbContext.cs`, `NSPC.Data/Data/Entity/Idm/idm_User.cs`
    *   `memory-bank/*` (All files for this update process)
    *   Cursor Rules: `agent-requested/rule-service-generation-agent` (applied)

## 3. Next Steps / Planned Work

*   **Immediate Next Steps (Post-Current Task):**
    *   **Crucial:** Resolve the root cause of the linter error "Utils is a type not a namespace" when `using NSPC.Common.Utils;` is attempted. This is essential for accessing common extension methods (like `GetPageAsync`) and utility classes (like `PredicateBuilder`).
    *   Based on the above, determine the correct way to implement predicate building (e.g., `LinqKit.CònTrước`, `PredicateBuilder`) and pagination (`GetPageAsync` or manual) in `ContractHandler.cs`.
    *   Fix any resulting compilation errors in `ContractHandler.cs`.
    *   Ensure `IContractHandler` and `ContractHandler` are correctly registered in `NSPC.API.V1/Program.cs`.
*   **Upcoming Features/Milestones:** A functional `sm_Contract` service layer.

## 4. Active Considerations & Open Questions

*   **Technical Challenges/Blockers:**
    *   **Primary Blocker:** The linter error preventing the use of the `NSPC.Common.Utils` namespace, which is assumed to contain `ExtensionMethods.GetPageAsync` and `PredicateBuilder`. Without resolving this, shared functionality cannot be reliably used, and the `GetPage` method refactoring is broken.
    *   The correct usage and namespace for `LinqKit.CònTrước` (user's current choice in `ContractHandler.cs`).
    *   Persistent type inference issue for `Helper.CreateExceptionResponse` in `ContractHandler.cs`'s `Delete` method.
*   **Risks:** The `sm_Contract` service remains non-functional until these namespace and dependency issues are resolved. Inconsistent data access patterns if shared utilities cannot be used.

## 5. Current State of Mind / Hypothesis

*   **Working Hypothesis:** The `ContractHandler.cs` requires common utilities/extensions from `NSPC.Common.Utils`. A persistent linter issue misidentifying `Utils` as a type (instead of a namespace) prevents the use of this namespace. This makes the recent `GetPage` refactoring (to use `GetPageAsync`) non-functional after the user removed the problematic `using` statement. The user's introduction of `LinqKit.CònTrước` is an attempt to bypass issues with an assumed `PredicateBuilder` in the problematic namespace. Validation logic has been successfully moved to the handler.
*   **Information Needed:**
    *   Definitive clarification on the structure of `NSPC.Common.Utils`: Is it a namespace? Does it directly contain a class named `Utils` causing a conflict? How should classes like `PredicateBuilder` and extension methods within it be accessed?
    *   Guidance on resolving the "Utils is a type" linter error.

_This document tracks the current work focus, recent changes, next steps, and active decisions or considerations. It is the most frequently updated document in the Memory Bank._ 