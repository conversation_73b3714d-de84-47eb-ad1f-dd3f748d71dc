﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.AdvanceRequest;
using NSPC.Business.Services.Contract;
using NSPC.Common;
using NSPC.Data.Data.Entity.JsonbEntity;

namespace NSPC.API.V1.Controllers.AdvanceRequest
{
    [ApiVersion("1.0")]
    [ApiController]
    [Authorize]
    [Route("api/v{api-version:apiVersion}/advance-requests")]
    [ApiExplorerSettings(GroupName = "Quản lý yêu cầu tạm ứng")]
    public class AdvanceRequestController : ControllerBase
    {
        private readonly IAdvanceRequestHandler _handler;

        public AdvanceRequestController(IAdvanceRequestHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Thêm mới yêu cầu tạm ứng
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreateAsync([FromBody] AdvanceRequestCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.CreateAsync(model, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách yêu cầu tạm ứng
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<AdvanceRequestQueryModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPageAsync([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<AdvanceRequestQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPageAsync(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xem chi tiết yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("{id}")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetByIdAsync(Guid id)
        {
            var result = await _handler.GetByIdAsync(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Cập nhật yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> UpdateAsync(Guid id, [FromBody] AdvanceRequestCreateUpdateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.UpdateAsync(id, model, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Xóa yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete, Route("{id}")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> DeleteAsync(Guid id)
        {
            var result = await _handler.DeleteAsync(id);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Gửi duyệt yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}/send")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SendAsync(Guid id)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.SendAsync(id, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Từ chối yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}/reject")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> RejectAsync(Guid id, [FromBody] jsonb_AdvanceRequestHistory model)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.RejectAsync(id, model, currentUser);

            return Helper.TransformData(result);
        }

        /// <summary>
        /// Phê duyệt yêu cầu tạm ứng
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPut, Route("{id}/approve")]
        [ProducesResponseType(typeof(Response<AdvanceRequestViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ApproveAsync(Guid id)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.ApproveAsync(id, currentUser);

            return Helper.TransformData(result);
        }
    }
}
