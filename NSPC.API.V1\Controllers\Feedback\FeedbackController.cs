﻿using Microsoft.AspNetCore.Mvc;
using NSPC.Business.Services.Feedback;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.Feedback

{
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v{api-version:apiVersion}/feedback")]
    [ApiExplorerSettings(GroupName = "Quản lý góp ý")]
    public class FeedbackController : ControllerBase
    {
        private readonly IFeedbackHandler _handler;

        public FeedbackController(IFeedbackHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo góp ý
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<FeedbackViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] FeedbackCreateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);

            var result = await _handler.Create(model, currentUser);
            return Helper.TransformData(result);
        }
    }
}
