﻿using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using NSPC.Data.Entity;
using SaleManagement.Data.Data.Entity.TaskHistory;
using Serilog;
using System.Linq.Expressions;
namespace NSPC.Business.Services.WorkItem
{
    public class TaskHandler : ITaskHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        public TaskHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
        }
        public async Task<Response<TaskViewModel>> Create(TaskCreateUpdateModel model)
        {
            try
            {
                if (model.IdTemplateStage == null || model.IdTemplateStage == Guid.Empty)
                    return Helper.CreateBadRequestResponse<TaskViewModel>("Vui lòng chọn giai đoạn cho công việc!");

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
                var entity = _mapper.Map<sm_Task>(model);
                entity.Id = Guid.NewGuid();
                entity.Code = await AutoGenerateAdvanceTasksCode("TN-");
                entity.CreatedByUserId = currentUser.UserId;
                entity.Status = TaskStatus.NotStarted;
                if (model.ApproverIds?.Any() == true)
                {
                    entity.Approvers = model.ApproverIds.Select(userId => new sm_TaskApprover
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId
                    }).ToList();
                    foreach (var approver in entity.Approvers)
                    {
                        var notification = new sm_TaskNotification
                        {
                            Id = Guid.NewGuid(),
                            ApprovalType = "Approver",
                            UserId = approver.UserId,
                            TaskId = entity.Id,
                            CreatedByUserName = currentUser.FullName,
                            NotificationStatus = NotificationStatus.Joined,
                            CreatedByUserId = currentUser.UserId,
                        };
                        _dbContext.Add(notification);
                    }
                }
                if (model.ExecutorIds?.Any() == true)
                {
                    entity.Executors = model.ExecutorIds.Select(userId => new sm_TaskExecutor
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId
                    }).ToList();
                    foreach (var executor in entity.Executors)
                    {
                        var notification = new sm_TaskNotification
                        {
                            Id = Guid.NewGuid(),
                            ApprovalType = "Executor",
                            UserId = executor.UserId,
                            TaskId = entity.Id,
                            CreatedByUserName = currentUser.FullName,
                            NotificationStatus = NotificationStatus.Joined,
                            CreatedByUserId = currentUser.UserId,
                        };
                        _dbContext.Add(notification);
                    }
                }
                if (model.SubTasks != null && model.SubTasks.Any())
                {
                    var subTaskEntities = new List<sm_SubTask>();

                    foreach (var subTaskModel in model.SubTasks)
                    {
                        var subTaskEntity = _mapper.Map<sm_SubTask>(subTaskModel);
                        subTaskEntity.Id = Guid.NewGuid();
                        subTaskEntity.TaskId = entity.Id;
                        subTaskEntity.CreatedByUserId = currentUser.UserId;
                        subTaskEntity.CreatedByUserName = currentUser.UserName;
                        subTaskEntity.CreatedOnDate = DateTime.Now;
                        subTaskEntities.Add(subTaskEntity);
                        if (subTaskModel.ExecutorIds != null && subTaskModel.ExecutorIds.Any())
                        {
                            foreach (var executorId in subTaskModel.ExecutorIds)
                            {
                                var subTaskExecutor = new sm_SubTaskExecutor
                                {
                                    Id = Guid.NewGuid(),
                                    SubTaskId = subTaskEntity.Id,
                                    UserId = executorId,
                                    CreatedByUserId = currentUser.UserId,
                                    CreatedByUserName = currentUser.UserName,
                                    CreatedOnDate = DateTime.Now
                                };
                                _dbContext.Add(subTaskExecutor);
                            }
                        }
                    }
                    entity.SubTasks = subTaskEntities;
                }
                //var construction = await _dbContext.sm_Construction
                //    .AsNoTracking()
                //    .Include(x => x.sm_ProjectTemplate)
                //    .ThenInclude(x => x.TemplateStages)
                //    .FirstOrDefaultAsync(x => x.Id == model.ConstructionId);
                //if (construction?.sm_ProjectTemplate?.TemplateStages.Any() == true)
                //{
                //    entity.TemplateStages = construction.sm_ProjectTemplate.TemplateStages.OrderBy(stage => stage.StepOrder)
                //        .Select(stage => new jsonb_TemplateStage
                //        {
                //            Id = stage.Id,
                //            StepOrder = stage.StepOrder,
                //            Name = stage.Name,
                //            Description = stage.Description,
                //            IsDone = false
                //        }).ToList();
                //}

                // Update history task
                var history = new sm_TaskUsageHistory
                {
                    Id = Guid.NewGuid(),
                    TaskId = entity.Id,
                    ActivityType = TaskActivityType.CreatedTask,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.FullName,
                };
                _dbContext.sm_TaskUsageHistory.Add(history);
                _dbContext.sm_Task.Add(entity);
                await _dbContext.SaveChangesAsync();
                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Thêm mới thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<TaskViewModel>> Delete(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_Task.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>(string.Format("Công việc này không tồn tại trong hệ thống!"));

                _dbContext.Remove(entity);
                await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Xóa thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@id}", id);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<TaskViewModel>> GetById(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_Task
                    .Include(x => x.TaskUsageHistories.OrderByDescending(h => h.CreatedOnDate))
                    .Include(x => x.Construction)
                    .Include(x => x.Executors)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.Approvers)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.SubTasks.OrderBy(h => h.CreatedOnDate))
                    .ThenInclude(x => x.SubTaskExecutors)
                    .ThenInclude(x => x.Idm_User)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Mã công việc không tồn tại trong hệ thống.");

                var result = _mapper.Map<TaskViewModel>(entity);
                return new Response<TaskViewModel>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}", id);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<Pagination<TaskViewModel>>> GetPage(TaskQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);

                var queryResult = _dbContext.sm_Task
                    .Include(x => x.Construction)
                    .Include(x => x.Executors)
                        .ThenInclude(x => x.Idm_User)
                    .Include(x => x.Approvers)
                        .ThenInclude(x => x.Idm_User)
                    .Include(x => x.SubTasks)
                    .AsNoTracking()
                .Where(predicate)
                .OrderBy(x => x.PriorityLevel)
                .ThenBy(x => x.Status)
                .ThenByDescending(x => x.CreatedOnDate);

                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<TaskViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<Pagination<TaskViewModel>>(ex);
            }
        }
        private Expression<Func<sm_Task, bool>> BuildQuery(TaskQueryModel query)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

            var predicate = PredicateBuilder.New<sm_Task>(true);
            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Name.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (query.ConstructionId != Guid.Empty)
                predicate.And(s => s.ConstructionId == query.ConstructionId);

            if (query.IdTemplateStage != Guid.Empty)
                predicate.And(s => s.IdTemplateStage == query.IdTemplateStage);

            if (!string.IsNullOrEmpty(query.Status))
                if (Enum.TryParse<TaskStatus>(query.Status, true, out var statusEnum))
                    predicate.And(s => s.Status == statusEnum);

            if (!string.IsNullOrEmpty(query.PriorityLevel))
                if (Enum.TryParse<PriorityLevel>(query.PriorityLevel, true, out var priorityLevelEnum))
                    predicate.And(s => s.PriorityLevel == priorityLevelEnum);

            if (query.DueDateRange != null && query.DueDateRange.Length > 0)
            {
                if (query.DueDateRange[0].HasValue)
                    predicate.And(x => x.StartDateTime >= query.DueDateRange[0].Value.Date);

                if (query.DueDateRange.Length > 1 && query.DueDateRange[1].HasValue)
                    predicate.And(x => x.EndDateTime <= query.DueDateRange[1].Value.Date.AddDays(1).AddTicks(-1));
            }

            return predicate;
        }

        public async Task<Response<TaskViewModel>> Update(Guid id, TaskCreateUpdateModel model)
        {
            try
            {
                var entity = await _dbContext.sm_Task
                    .Include(x => x.Approvers)
                    .Include(x => x.Executors)
                    .Include(x => x.SubTasks)
                    .ThenInclude(st => st.SubTaskExecutors)
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Công việc không tồn tại trong hệ thống!");

                if (entity.Status == TaskStatus.PendingApproval || entity.Status == TaskStatus.Passed)
                    return Helper.CreateBadRequestResponse<TaskViewModel>("Không thể chỉnh sửa công việc khi đang ở trạng thái 'Chờ duyệt' hoặc 'Đạt'.");

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                // Thêm lịch sử chỉnh sửa
                _dbContext.sm_TaskUsageHistory.Add(new sm_TaskUsageHistory
                {
                    Id = Guid.NewGuid(),
                    TaskId = entity.Id,
                    ActivityType = TaskActivityType.UpdatedTaskInfo,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.FullName,
                });

                // Thêm lịch sử tải file
                var oldAttachments = entity.Attachments ?? new List<jsonb_Attachment>();
                var newAttachments = model.Attachments ?? new List<jsonb_Attachment>();
                var addedAttachmentIds = newAttachments.Where(x => !oldAttachments.Any(a => a.Id == x.Id))
                                                        .Select(a => a.Id.ToString())
                                                        .Where(x => !string.IsNullOrEmpty(x))
                                                        .ToList();
                if (addedAttachmentIds.Any())
                {
                    _dbContext.sm_TaskUsageHistory.Add(new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.UploadedAttachment,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    });
                }

                entity.Name = model.Name;
                entity.StartDateTime = model.StartDateTime;
                entity.EndDateTime = model.EndDateTime;
                entity.PriorityLevel = Enum.TryParse<PriorityLevel>(model.PriorityLevel, true, out var priorityLevelEnum) ? priorityLevelEnum : PriorityLevel.Low;
                entity.Attachments = model.Attachments;
                entity.Description = model.Description;
                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.UserName;
                entity.LastModifiedOnDate = DateTime.Now;

                // Update Approvers
                var modelApproverIds = model.ApproverIds ?? new List<Guid>();
                var existingApprovers = entity.Approvers.ToList();
                var existingApproverUserIds = existingApprovers
                    .Where(a => a.UserId.HasValue)
                    .Select(a => a.UserId.Value)
                    .ToList();

                // Remove approvers that are not in the new list
                var toRemoveApprovers = existingApprovers
                    .Where(a => a.UserId.HasValue && !modelApproverIds.Contains(a.UserId.Value))
                    .ToList();
                foreach (var approver in toRemoveApprovers)
                {
                    var notification = new sm_TaskNotification
                    {
                        Id = Guid.NewGuid(),
                        ApprovalType = "Approver",
                        UserId = approver.UserId,
                        TaskId = entity.Id,
                        CreatedByUserName = currentUser.FullName,
                        NotificationStatus = NotificationStatus.Left,
                        CreatedByUserId = currentUser.UserId,
                    };
                    _dbContext.Add(notification);
                    _dbContext.Remove(approver);
                }

                // Add new approvers that do not exist in the current approvers
                var toAddApproverIds = modelApproverIds
                    .Except(existingApproverUserIds)
                    .ToList();
                foreach (var userId in toAddApproverIds)
                {
                    var newApprover = new sm_TaskApprover
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId,
                        CreatedByUserId = currentUser.UserId,
                        CreatedOnDate = DateTime.Now
                    };
                    entity.Approvers.Add(newApprover);
                    var notification = new sm_TaskNotification
                    {
                        Id = Guid.NewGuid(),
                        ApprovalType = "Approver",
                        UserId = newApprover.UserId,
                        TaskId = entity.Id,
                        CreatedByUserName = currentUser.FullName,
                        NotificationStatus = NotificationStatus.Joined,
                        CreatedByUserId = currentUser.UserId,
                    };
                    _dbContext.Add(notification);
                    _dbContext.Add(newApprover);
                }

                // Check if the set of approvers has changed
                bool isApproverChanged = !new HashSet<Guid>(modelApproverIds).SetEquals(existingApproverUserIds);

                if (isApproverChanged)
                {
                    var historyChanged = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.ChangedAssignee,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historyChanged);
                }

                // Update Executors
                var modelExecutorIds = model.ExecutorIds ?? new List<Guid>();
                var existingExecutors = entity.Executors.ToList();
                var existingExecutorUserIds = existingExecutors
                    .Where(a => a.UserId.HasValue)
                    .Select(a => a.UserId.Value)
                    .ToList();

                // Remove executors that are not in the new list
                var toRemoveExecutors = existingExecutors
                    .Where(a => a.UserId.HasValue && !modelExecutorIds.Contains(a.UserId.Value))
                    .ToList();
                foreach (var executor in toRemoveExecutors)
                {
                    var notification = new sm_TaskNotification
                    {
                        Id = Guid.NewGuid(),
                        ApprovalType = "Executor",
                        UserId = executor.UserId,
                        TaskId = entity.Id,
                        CreatedByUserName = currentUser.FullName,
                        NotificationStatus = NotificationStatus.Left,
                        CreatedByUserId = currentUser.UserId,
                    };
                    _dbContext.Add(notification);
                    _dbContext.Remove(executor);
                }
                // Add new executors that do not exist in the current executors
                var toAddExecutorIds = modelExecutorIds
                    .Except(existingExecutorUserIds)
                    .ToList();
                foreach (var userId in toAddExecutorIds)
                {
                    var newExecutor = new sm_TaskExecutor
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId,
                        CreatedByUserId = currentUser.UserId,
                        CreatedOnDate = DateTime.Now
                    };
                    entity.Executors.Add(newExecutor);
                    var notification = new sm_TaskNotification
                    {
                        Id = Guid.NewGuid(),
                        ApprovalType = "Executor",
                        UserId = newExecutor.UserId,
                        TaskId = entity.Id,
                        CreatedByUserName = currentUser.FullName,
                        NotificationStatus = NotificationStatus.Joined,
                        CreatedByUserId = currentUser.UserId,
                    };
                    _dbContext.Add(notification);
                    _dbContext.Add(newExecutor);
                }
                // Check if the set of executors has changed
                bool isExecutorChanged = !new HashSet<Guid>(modelExecutorIds).SetEquals(existingExecutorUserIds);

                if (isExecutorChanged)
                {
                    var historyChanged = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.ChangedApprover,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historyChanged);
                }

                // Update SubTasks
                var modelSubTasks = model.SubTasks ?? new List<SubTaskCreateUpdateModel>();
                var existingSubTasks = entity.SubTasks.ToList();
                var modelSubTaskIds = modelSubTasks.Where(s => s.Id != Guid.Empty).Select(s => s.Id).ToList();
                // Remove deleted subtasks
                var toRemoveSubTasks = existingSubTasks.Where(s => !modelSubTaskIds.Contains(s.Id)).ToList();
                foreach (var subTask in toRemoveSubTasks)
                {
                    var subTaskHistory = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.DeletedSubtask,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                        NameSubtask = subTask.Name
                    };
                    _dbContext.sm_TaskUsageHistory.Add(subTaskHistory);
                    _dbContext.Remove(subTask);
                }
                bool isUpdateSubTask = false;
                bool isChangedSubtaskAssignee = false;
                // Update or add subtasks
                foreach (var subTaskModel in modelSubTasks)
                {
                    var subTaskEntity = existingSubTasks.FirstOrDefault(s => s.Id == subTaskModel.Id);
                    if (subTaskEntity != null)
                    {
                        if (subTaskEntity.Name != subTaskModel.Name ||
                            subTaskEntity.DueDate != subTaskModel.DueDate
                            )
                            isUpdateSubTask = true;

                        // Thêm lịch sử tải file cho công việc con
                        var oldSubTaskAttachments = subTaskEntity.Attachments ?? new List<jsonb_Attachment>();
                        var newSubTaskAttachments = subTaskModel.Attachments ?? new List<jsonb_Attachment>();
                        var addedSubTaskAttachmentIds = newSubTaskAttachments
                            .Where(x => !oldSubTaskAttachments.Any(a => a.Id == x.Id))
                            .Select(a => a.Id.ToString())
                            .Where(x => !string.IsNullOrEmpty(x))
                            .ToList();
                        if (addedSubTaskAttachmentIds.Any())
                        {
                            _dbContext.sm_TaskUsageHistory.Add(new sm_TaskUsageHistory
                            {
                                Id = Guid.NewGuid(),
                                TaskId = entity.Id,
                                ActivityType = TaskActivityType.UploadedSubtaskAttachment,
                                CreatedByUserId = currentUser.UserId,
                                CreatedByUserName = currentUser.FullName,
                                NameSubtask = subTaskModel.Name
                            });
                        }

                        if (subTaskModel.IsCompleted == true && subTaskEntity.IsCompleted != subTaskModel.IsCompleted)
                        {
                            var subTaskHistory = new sm_TaskUsageHistory
                            {
                                Id = Guid.NewGuid(),
                                TaskId = entity.Id,
                                ActivityType = TaskActivityType.MarkedSubtaskCompleted,
                                CreatedByUserId = currentUser.UserId,
                                CreatedByUserName = currentUser.FullName,
                                NameSubtask = subTaskModel.Name
                            };
                            _dbContext.sm_TaskUsageHistory.Add(subTaskHistory);
                        }
                        subTaskEntity.IsCompleted = subTaskModel.IsCompleted;
                        subTaskEntity.DueDate = subTaskModel.DueDate;
                        subTaskEntity.Name = subTaskModel.Name;
                        subTaskEntity.LastModifiedByUserId = currentUser.UserId;
                        subTaskEntity.LastModifiedByUserName = currentUser.UserName;
                        subTaskEntity.LastModifiedOnDate = DateTime.Now;
                        subTaskEntity.Attachments = subTaskModel.Attachments;

                        // --- Xử lý sm_SubTaskExecutor ---
                        var modelSubTaskExecutorIds = subTaskModel.ExecutorIds ?? new List<Guid>();
                        var existingSubTaskExecutors = subTaskEntity.SubTaskExecutors?.ToList() ?? new List<sm_SubTaskExecutor>();
                        var existingSubTaskExecutorUserIds = existingSubTaskExecutors
                            .Where(e => e.UserId.HasValue)
                            .Select(e => e.UserId.Value)
                            .ToList();

                        // Xóa executor không còn trong danh sách mới
                        var toRemoveSubTaskExecutors = existingSubTaskExecutors
                            .Where(e => e.UserId.HasValue && !modelSubTaskExecutorIds.Contains(e.UserId.Value))
                            .ToList();
                        // Thêm executor mới
                        var toAddSubTaskExecutorIds = modelSubTaskExecutorIds
                            .Except(existingSubTaskExecutorUserIds)
                            .ToList();

                        // Check if subtask assignee changed
                        if (toRemoveSubTaskExecutors.Any() || toAddSubTaskExecutorIds.Any())
                        {
                            isChangedSubtaskAssignee = true;
                        }

                        foreach (var executor in toRemoveSubTaskExecutors)
                        {
                            _dbContext.Remove(executor);
                        }

                        foreach (var userId in toAddSubTaskExecutorIds)
                        {
                            var newExecutor = new sm_SubTaskExecutor
                            {
                                Id = Guid.NewGuid(),
                                SubTaskId = subTaskEntity.Id,
                                UserId = userId,
                                CreatedByUserId = currentUser.UserId,
                                CreatedByUserName = currentUser.UserName,
                                CreatedOnDate = DateTime.Now
                            };
                            _dbContext.Add(newExecutor);
                        }
                        // --- Kết thúc xử lý sm_SubTaskExecutor ---

                        if (isChangedSubtaskAssignee)
                        {
                            var historyChangedSubtaskAssignee = new sm_TaskUsageHistory
                            {
                                Id = Guid.NewGuid(),
                                TaskId = entity.Id,
                                ActivityType = TaskActivityType.ChangedSubtaskAssignee,
                                CreatedByUserId = currentUser.UserId,
                                CreatedByUserName = currentUser.FullName,
                                NameSubtask = subTaskModel.Name
                            };
                            _dbContext.sm_TaskUsageHistory.Add(historyChangedSubtaskAssignee);
                            isChangedSubtaskAssignee = false;
                        }
                    }
                    else
                    {
                        var newSubTask = _mapper.Map<sm_SubTask>(subTaskModel);
                        newSubTask.Id = Guid.NewGuid();
                        newSubTask.TaskId = entity.Id;
                        newSubTask.CreatedByUserId = currentUser.UserId;
                        newSubTask.CreatedByUserName = currentUser.UserName;
                        newSubTask.CreatedOnDate = DateTime.Now;
                        entity.SubTasks.Add(newSubTask);
                        // Thêm executor cho subtask mới
                        if (subTaskModel.ExecutorIds != null && subTaskModel.ExecutorIds.Any())
                        {
                            foreach (var executorId in subTaskModel.ExecutorIds)
                            {
                                var subTaskExecutor = new sm_SubTaskExecutor
                                {
                                    Id = Guid.NewGuid(),
                                    SubTaskId = newSubTask.Id,
                                    UserId = executorId,
                                    CreatedByUserId = currentUser.UserId,
                                    CreatedByUserName = currentUser.UserName,
                                    CreatedOnDate = DateTime.Now
                                };
                                _dbContext.Add(subTaskExecutor);
                            }
                        }
                        _dbContext.Add(newSubTask);
                    }
                }

                if (isUpdateSubTask)
                {
                    var historySubTask = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.UpdateSubTask,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historySubTask);
                }
                await _dbContext.SaveChangesAsync();

                string message = isUpdateSubTask
                    ? $"{currentUser.FullName} đã chỉnh sửa thông tin công việc con"
                    : "Chỉnh sửa thành công";

                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }
        public async Task<string> AutoGenerateAdvanceTasksCode(string defaultPrefix)
        {
            try
            {
                var code = defaultPrefix + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_Task
                    .AsNoTracking()
                    .Where(x => x.Code.Contains(code))
                    .OrderByDescending(x => x.CreatedOnDate)
                    .FirstOrDefaultAsync();

                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("", ex);
                return string.Empty;
            }
        }
        public async Task<Response> DeleteMany(List<Guid> ids)
        {
            try
            {
                var entities = await _dbContext.sm_Task.Where(x => ids.Contains(x.Id)).ToListAsync();
                if (entities == null || entities.Count == 0)
                    return Helper.CreateNotFoundResponse<int>("Không tìm thấy công việc nào để xóa!");

                _dbContext.sm_Task.RemoveRange(entities);
                var affected = await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse($"Đã xóa {entities.Count} công việc thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Ids: {@ids}", ids);
                return Helper.CreateExceptionResponse<int>(ex);
            }
        }
        public async Task<Response<TaskViewModel>> UpdateStatus(Guid id, string status, string description)
        {
            try
            {
                var entity = await _dbContext.sm_Task.Include(x => x.Executors).FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Công việc không tồn tại trong hệ thống!");

                if (!Enum.TryParse<TaskStatus>(status, true, out var parsedStatus))
                    return Helper.CreateNotFoundResponse<TaskViewModel>($"Gía trị trạng thái không hợp lệ: {status}");

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                entity.Status = parsedStatus;
                entity.Description = description;
                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.UserName;
                entity.LastModifiedOnDate = DateTime.Now;

                if (status == "InProgress")
                {
                    var historySubTask = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.StartTask,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historySubTask);
                }
                if (status == "PendingApproval")
                {
                    var historySubTask = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.SubmittedForApproval,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historySubTask);

                    // Lấy người phê duyệt và thêm vào sm_TaskNotification
                    if (entity?.Executors?.Any() == true)
                        foreach (var executor in entity?.Executors)
                        {
                            var notification = new sm_TaskNotification
                            {
                                Id = Guid.NewGuid(),
                                ApprovalType = "Executor",
                                UserId = executor.UserId,
                                TaskId = entity.Id,
                                CreatedByUserName = currentUser.FullName,
                                NotificationStatus = NotificationStatus.Approved,
                                CreatedByUserId = currentUser.UserId,
                            };
                            _dbContext.Add(notification);
                        }
                }
                if (status == "Passed")
                {
                    var historySubTask = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.MarkedAsPassed,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historySubTask);
                }
                if (status == "Failed")
                {
                    var historySubTask = new sm_TaskUsageHistory
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        ActivityType = TaskActivityType.MarkedAsFailed,
                        Description = entity.Description,
                        CreatedByUserId = currentUser.UserId,
                        CreatedByUserName = currentUser.FullName,
                    };
                    _dbContext.sm_TaskUsageHistory.Add(historySubTask);
                }
                await _dbContext.SaveChangesAsync();
                string message = "Cập nhật trạng thái thành công";
                if (status == "PendingApproval")
                    message = "Đã gửi duyệt công việc thành công.";
                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, Status: {@status}, Description: {@description}", id, status, description);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }
        public async Task<Response<Pagination<TaskViewModel>>> GetPageByConstructionId(Guid constructionId, TaskQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);
                predicate = predicate.And(x => x.ConstructionId == constructionId);

                var queryResult = _dbContext.sm_Task
                    .Include(x => x.Construction)
                    .Include(x => x.Executors)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.Approvers)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.SubTasks)
                    .AsNoTracking()
                    .Where(predicate);

                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<TaskViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: ConstructionId: {@constructionId}, Query: {@query}", constructionId, query);
                return Helper.CreateExceptionResponse<Pagination<TaskViewModel>>(ex);
            }
        }
        public async Task<Response<List<TaskViewModel>>> UpdateStatusMany(List<Guid> ids, string status, string description)
        {
            try
            {
                if (!Enum.TryParse<TaskStatus>(status, true, out var parsedStatus))
                    return Helper.CreateNotFoundResponse<List<TaskViewModel>>($"Gía trị trạng thái không hợp lệ: {status}");

                var entities = await _dbContext.sm_Task
                    .Include(x => x.Executors)
                    .Where(x => ids.Contains(x.Id))
                    .ToListAsync();

                if (entities == null || entities.Count == 0)
                    return Helper.CreateNotFoundResponse<List<TaskViewModel>>("Không tìm thấy công việc nào để cập nhật!");

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                foreach (var entity in entities)
                {
                    entity.Status = parsedStatus;
                    entity.Description = description;
                    entity.LastModifiedByUserId = currentUser.UserId;
                    entity.LastModifiedByUserName = currentUser.UserName;
                    entity.LastModifiedOnDate = DateTime.Now;

                    if (status == "InProgress")
                    {
                        var history = new sm_TaskUsageHistory
                        {
                            Id = Guid.NewGuid(),
                            TaskId = entity.Id,
                            ActivityType = TaskActivityType.StartTask,
                            CreatedByUserId = currentUser.UserId,
                            CreatedByUserName = currentUser.FullName,
                        };
                        _dbContext.sm_TaskUsageHistory.Add(history);
                    }
                    if (status == "PendingApproval")
                    {
                        var history = new sm_TaskUsageHistory
                        {
                            Id = Guid.NewGuid(),
                            TaskId = entity.Id,
                            ActivityType = TaskActivityType.SubmittedForApproval,
                            CreatedByUserId = currentUser.UserId,
                            CreatedByUserName = currentUser.FullName,
                        };
                        _dbContext.sm_TaskUsageHistory.Add(history);

                        if (entity?.Executors?.Any() == true)
                        {
                            foreach (var executor in entity.Executors)
                            {
                                var notification = new sm_TaskNotification
                                {
                                    Id = Guid.NewGuid(),
                                    ApprovalType = "Executor",
                                    UserId = executor.UserId,
                                    TaskId = entity.Id,
                                    CreatedByUserName = currentUser.FullName,
                                    NotificationStatus = NotificationStatus.Approved,
                                    CreatedByUserId = currentUser.UserId,
                                };
                                _dbContext.Add(notification);
                            }
                        }
                    }
                    if (status == "Passed")
                    {
                        var history = new sm_TaskUsageHistory
                        {
                            Id = Guid.NewGuid(),
                            TaskId = entity.Id,
                            ActivityType = TaskActivityType.MarkedAsPassed,
                            CreatedByUserId = currentUser.UserId,
                            CreatedByUserName = currentUser.FullName,
                        };
                        _dbContext.sm_TaskUsageHistory.Add(history);
                    }
                    if (status == "Failed")
                    {
                        var history = new sm_TaskUsageHistory
                        {
                            Id = Guid.NewGuid(),
                            TaskId = entity.Id,
                            ActivityType = TaskActivityType.MarkedAsFailed,
                            Description = entity.Description,
                            CreatedByUserId = currentUser.UserId,
                            CreatedByUserName = currentUser.FullName,
                        };
                        _dbContext.sm_TaskUsageHistory.Add(history);
                    }
                }

                await _dbContext.SaveChangesAsync();
                string message = "Cập nhật trạng thái thành công";
                if (status == "PendingApproval")
                    message = "Đã gửi duyệt công việc thành công.";

                var result = _mapper.Map<List<TaskViewModel>>(entities);
                return Helper.CreateSuccessResponse(result, message);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Ids: {@ids}, Status: {@status}, Description: {@description}", ids, status, description);
                return Helper.CreateExceptionResponse<List<TaskViewModel>>(ex);
            }
        }
    }
}
