﻿using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using NSPC.Data.Entity;
using SaleManagement.Data.Data.Entity.TaskHistory;
using Serilog;
using System.Linq.Expressions;
namespace NSPC.Business.Services.WorkItem
{
    public class TaskHandler : ITaskHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        public TaskHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
        }
        public async Task<Response<TaskViewModel>> Create(TaskCreateUpdateModel model)
        {
            try
            {
                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);
                var entity = _mapper.Map<sm_Task>(model);
                entity.Id = Guid.NewGuid();
                entity.Code = await AutoGenerateAdvanceRequestCode("TN-");
                entity.CreatedByUserId = currentUser.UserId;
                entity.Status = TaskStatus.NotStarted;
                if (model.ApproverIds?.Any() == true)
                {
                    entity.Approvers = model.ApproverIds.Select(userId => new sm_TaskApprover
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId
                    }).ToList();
                }
                if (model.ExecutorIds?.Any() == true)
                {
                    entity.Executors = model.ExecutorIds.Select(userId => new sm_TaskExecutor
                    {
                        Id = Guid.NewGuid(),
                        TaskId = entity.Id,
                        UserId = userId
                    }).ToList();
                }
                //var construction = await _dbContext.sm_Construction
                //    .AsNoTracking()
                //    .Include(x => x.sm_ProjectTemplate)
                //    .ThenInclude(x => x.TemplateStages)
                //    .FirstOrDefaultAsync(x => x.Id == model.ConstructionId);
                //if (construction?.sm_ProjectTemplate?.TemplateStages.Any() == true)
                //{
                //    entity.TemplateStages = construction.sm_ProjectTemplate.TemplateStages.OrderBy(stage => stage.StepOrder)
                //        .Select(stage => new jsonb_TemplateStage
                //        {
                //            Id = stage.Id,
                //            StepOrder = stage.StepOrder,
                //            Name = stage.Name,
                //            Description = stage.Description,
                //            IsDone = false
                //        }).ToList();
                //}

                var history = new sm_TaskUsageHistory
                {
                    Id = Guid.NewGuid(),
                    TaskId = entity.Id,
                    ActivityType = TaskActivityType.CreatedTask,
                    //ExecutionDate = model.IncidentDate,
                    //Description = model.Description,
                    CreatedByUserId = currentUser.UserId,
                    CreatedByUserName = currentUser.UserName,
                };
                _dbContext.sm_TaskUsageHistory.Add(history);
                _dbContext.sm_Task.Add(entity);
                await _dbContext.SaveChangesAsync();
                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Thêm mới thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<TaskViewModel>> Delete(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_Task.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>(string.Format("Công việc này không tồn tại trong hệ thống!"));

                _dbContext.Remove(entity);
                await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Xóa thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@id}", id);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<TaskViewModel>> GetById(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_Task
                    .Include(x => x.Construction)
                    .Include(x => x.Executors)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.Approvers)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.SubTasks)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Mã công việc không tồn tại trong hệ thống.");

                var result = _mapper.Map<TaskViewModel>(entity);
                return new Response<TaskViewModel>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}", id);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }

        public async Task<Response<Pagination<TaskViewModel>>> GetPage(TaskQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);

                var queryResult = _dbContext.sm_Task.Include(x => x.Construction)
                    .Include(x => x.Executors)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.Approvers)
                    .ThenInclude(x => x.Idm_User)
                    .Include(x => x.SubTasks)
                    .AsNoTracking().Where(predicate);
                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<TaskViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<Pagination<TaskViewModel>>(ex);
            }
        }
        private Expression<Func<sm_Task, bool>> BuildQuery(TaskQueryModel query)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

            var predicate = PredicateBuilder.New<sm_Task>(true);
            if (!string.IsNullOrEmpty(query.FullTextSearch))
                predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Name.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (query.ConstructionId != Guid.Empty)
                predicate.And(s => s.ConstructionId == query.ConstructionId);

            if (!string.IsNullOrEmpty(query.Status))
                if (Enum.TryParse<TaskStatus>(query.Status, true, out var statusEnum))
                    predicate.And(s => s.Status == statusEnum);

            if (!string.IsNullOrEmpty(query.PriorityLevel))
                if (Enum.TryParse<PriorityLevel>(query.PriorityLevel, true, out var priorityLevelEnum))
                    predicate.And(s => s.PriorityLevel == priorityLevelEnum);

            if (query.DueDateRange != null && query.DueDateRange.Length > 0)
            {
                if (query.DueDateRange[0].HasValue)
                    predicate.And(x => x.StartDateTime >= query.DueDateRange[0].Value.Date);

                if (query.DueDateRange.Length > 1 && query.DueDateRange[1].HasValue)
                    predicate.And(x => x.EndDateTime <= query.DueDateRange[1].Value.Date.AddDays(1).AddTicks(-1));
            }

            return predicate;
        }

        public async Task<Response<TaskViewModel>> Update(Guid id, TaskCreateUpdateModel model)
        {
            try
            {
                var entity = await _dbContext.sm_Task
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Công việc không tồn tại trong hệ thống!");


                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                entity.Name = model.Name;
                //entity.Description = model.Description;
                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.UserName;
                entity.LastModifiedOnDate = DateTime.Now;

                // Update TemplateStages
                //var existingStages = entity.TemplateStages.ToList();
                //var modelStages = model.TemplateStages ?? new List<TemplateStageCreateUpdateModel>();
                //// Update or add stages
                //for (int i = 0; i < modelStages.Count; i++)
                //{
                //    var stageModel = modelStages[i];
                //    sm_TemplateStage stageEntity = null;

                //    if (stageModel.Id.HasValue)
                //    {
                //        stageEntity = existingStages.FirstOrDefault(s => s.Id == stageModel.Id.Value);
                //    }

                //    if (stageEntity != null)
                //    {
                //        stageEntity.Name = stageModel.Name;
                //        stageEntity.Description = stageModel.Description;
                //        stageEntity.StepOrder = i + 1;
                //        stageEntity.LastModifiedByUserId = currentUser.UserId;
                //        stageEntity.LastModifiedOnDate = DateTime.Now;
                //    }
                //    else
                //    {
                //        var newStage = _mapper.Map<sm_TemplateStage>(stageModel);
                //        newStage.Id = Guid.NewGuid();
                //        newStage.TaskId = entity.Id;
                //        newStage.StepOrder = i + 1;
                //        newStage.CreatedByUserId = currentUser.UserId;
                //        newStage.CreatedOnDate = DateTime.Now;
                //        entity.TemplateStages.Add(newStage);
                //        _dbContext.Add(newStage);
                //    }
                //}
                //var modelStageIds = modelStages.Where(s => s.Id.HasValue).Select(s => s.Id.Value).ToList();
                //var stagesToRemove = existingStages.Where(s => !modelStageIds.Contains(s.Id)).ToList();

                //foreach (var stage in stagesToRemove)
                //{
                //    _dbContext.Remove(stage);
                //}

                await _dbContext.SaveChangesAsync();
                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Chỉnh sửa thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Model: {@model}", model);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }
        public async Task<string> AutoGenerateAdvanceRequestCode(string defaultPrefix)
        {
            try
            {
                var code = defaultPrefix + DateTime.Now.ToString("ddMMyy");

                var result = await _dbContext.sm_Task
                    .AsNoTracking()
                    .Where(x => x.Code.Contains(code))
                    .OrderByDescending(x => x.CreatedOnDate)
                    .FirstOrDefaultAsync();

                if (result != null)
                {
                    var currentNum = result.Code.Substring(result.Code.Length - 3, 3);
                    var currentNumInt = int.Parse(currentNum) + 1;
                    var stringResult = "";
                    if (currentNumInt < 10)
                    {
                        stringResult = "00" + currentNumInt;
                    }
                    else if (currentNumInt >= 10 && currentNumInt < 100)
                    {
                        stringResult = "0" + currentNumInt;
                    }
                    else
                    {
                        stringResult = currentNumInt.ToString();
                    }

                    return code + stringResult;
                }
                else
                {
                    return code + "001";
                }
            }
            catch (Exception ex)
            {
                Log.Error("", ex);
                return string.Empty;
            }
        }
        public async Task<Response> DeleteMany(List<Guid> ids)
        {
            try
            {
                var entities = await _dbContext.sm_Task.Where(x => ids.Contains(x.Id)).ToListAsync();
                if (entities == null || entities.Count == 0)
                    return Helper.CreateNotFoundResponse<int>("Không tìm thấy công việc nào để xóa!");

                _dbContext.sm_Task.RemoveRange(entities);
                var affected = await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse($"Đã xóa {entities.Count} công việc thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Ids: {@ids}", ids);
                return Helper.CreateExceptionResponse<int>(ex);
            }
        }
        public async Task<Response<TaskViewModel>> UpdateStatus(Guid id, string status)
        {
            try
            {
                var entity = await _dbContext.sm_Task.FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskViewModel>("Công việc không tồn tại trong hệ thống!");

                if (!Enum.TryParse<TaskStatus>(status, true, out var parsedStatus))
                    return Helper.CreateNotFoundResponse<TaskViewModel>($"Gía trị trạng thái không hợp lệ: {status}");

                var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

                entity.Status = parsedStatus;
                entity.LastModifiedByUserId = currentUser.UserId;
                entity.LastModifiedByUserName = currentUser.UserName;
                entity.LastModifiedOnDate = DateTime.Now;

                await _dbContext.SaveChangesAsync();
                return Helper.CreateSuccessResponse(_mapper.Map<TaskViewModel>(entity), "Cập nhật trạng thái thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}, Status: {@status}", id, status);
                return Helper.CreateExceptionResponse<TaskViewModel>(ex);
            }
        }
    }
}
