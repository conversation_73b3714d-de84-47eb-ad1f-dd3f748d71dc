﻿using NSPC.Business;
using NSPC.Common;
using NSPC.Common.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Net;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Linq;

namespace NSPC.Api.Controllers
{
    /// <summary>
    ///     Core - Upload
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Route("api/v1/me")]
    [ApiExplorerSettings(GroupName = "Dashboard - User")]
    public class UserDashboardController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly IUserHandler _userHandler;
        private readonly INotificationHandler _notificationHandler;
        public UserDashboardController(IConfiguration configuration,
            IUserHandler userHandler, INotificationHandler notificationHandler)
        {
            _configuration = configuration;
            _userHandler = userHandler;
            _notificationHandler = notificationHandler;
        }
    
        /// <summary>
        /// Cập nhật mật khẩu cho bản thân
        /// </summary>
        /// <param name="id">Id bản ghi</param>
        /// <param name="oldPassword">Mật khẩu cũ</param>
        /// <param name="password">Mật khẩu mới</param>
        /// <returns>Kết quả trả về</returns>
        /// <response code="200">Thành công</response>
        [Authorize, HttpPut, Route("password")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChangePasswordAsync([FromBody] UserUpdatePasswordModel model)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            // Call service
            var result = await _userHandler.ChangePasswordAsync(model);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Get dashboard info
        /// </summary>
        /// <returns></returns>
        [HttpGet, Authorize, Route("info")]
        [ProducesResponseType(typeof(Response<UserModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetDashboardInfo()
        {
            var requestInfo = Helper.GetRequestInfo(Request);

            var user = await _userHandler.GetDetail(requestInfo.UserId, requestInfo.Language);

            return Helper.TransformData(user);
        }

        /// <summary>
        /// Forgot password
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpPut, Route("forgot-password/{email}")]
        [ProducesResponseType(typeof(Response<UserModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> ForgotPassword(string email)
        {
            // Call service
            var result = await _userHandler.ForgotPassword(email);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Forgot password
        /// </summary>
        /// <param name="email"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpPut, Route("verify-forgot-password")]
        [ProducesResponseType(typeof(Response<UserModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> VerifyForgotPasswordToken(VerifyForgotPasswordTokenModel model)
        {
            // Call service
            var result = await _userHandler.VerifyForgotPasswordToken(model.ResetPasswordToken);

            // Hander response
            return Helper.TransformData(result);
        }



        /// <summary>
        /// Reset password with token
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpPut, Route("reset-password")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ResetPassword([FromBody] UserResetPasswordModel model)
        {
            // Get Token Info
            var requestInfo = Helper.GetRequestInfo(Request);

            // Call service
            var result = await _userHandler.ResetPassword(model.ResetPasswordToken, model.Password);

            // Hander response
            return Helper.TransformData(result);
        }


        /// <summary>
        /// Verify email
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous, HttpPut, Route("verify-email")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> VerifyEmail([FromBody] UserVerifyEmailModel model)
        {
            // Call service
            var result = await _userHandler.VerifyEmail(model.Email, model.Token);

            // Hander response
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Switch Profile
        /// </summary>
        /// <returns></returns>
        [HttpPut, Authorize, Route("profile/switch")]
        [ProducesResponseType(typeof(Response<LoginResponse>), StatusCodes.Status200OK)]
        public async Task<IActionResult> SwitchProfile(ProfileTypeModel model)
        {
            var requestInfo = Helper.GetRequestInfo(Request);

            var result = await _userHandler.SwitchProfile(model);

            return Helper.TransformData(result);
        }


        /// <summary>
        /// UpdatePaid user avatar url
        /// </summary>
        /// <returns></returns>
        [HttpPut, Authorize, Route("info/avatar")]
        [ProducesResponseType(typeof(Response<UserModel>), StatusCodes.Status200OK)]

        public async Task<IActionResult> UpdateAvatarUrl([FromBody] UserSetAvatarUrlModel model)
        {
            var requestInfo = Helper.GetRequestInfo(Request);

            model.UserId = requestInfo.UserId;

            var result = await _userHandler.UpdateAvatarUrl(model);

            return Helper.TransformData(result);
        }

   




        /// <summary>
        /// UpdatePaid location
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Authorize, Route("location")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> ChangeStatus(UserLocationrUpdateModel model)
        {
            var requestInfo = Helper.GetRequestInfo(Request);
            var response = await _userHandler.UpdateLocation(model);
            return Helper.TransformData(response);
        }
        /// <summary>
        /// switch status
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut, Authorize, Route("status")]
        [ProducesResponseType(typeof(Response), StatusCodes.Status200OK)]
        public async Task<IActionResult> SwitchStatus(UserStatusUpdateModel model)
        {
            var requestInfo = Helper.GetRequestInfo(Request);
            var response = await _userHandler.SwitchStatus(model);
            return Helper.TransformData(response);
        }
    }
}