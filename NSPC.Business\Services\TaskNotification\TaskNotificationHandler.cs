﻿using AutoMapper;
using LinqKit;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using NSPC.Common;
using NSPC.Data;
using NSPC.Data.Data;
using Serilog;
using System.Linq.Expressions;
namespace NSPC.Business.Services.TaskNotification
{
    public class TaskNotificationHandler : ITaskNotificationHandler
    {
        private readonly SMDbContext _dbContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMapper _mapper;
        private readonly string _staticsFolder;
        public TaskNotificationHandler(SMDbContext dbContext, IHttpContextAccessor httpContextAccessor, IMapper mapper)
        {
            _dbContext = dbContext;
            _httpContextAccessor = httpContextAccessor;
            _mapper = mapper;
        }

        public async Task<Response<TaskNotificationViewModel>> GetById(Guid id)
        {
            try
            {
                var entity = await _dbContext.sm_TaskNotification
                    .AsNoTracking()
                    .Include(x => x.Task)
                    .Include(x => x.idm_User)
                    .Include(x => x.CreatedByUser)
                    .FirstOrDefaultAsync(x => x.Id == id);
                if (entity == null)
                    return Helper.CreateNotFoundResponse<TaskNotificationViewModel>("Lịch sử công việc không tồn tại trong hệ thống.");

                var result = _mapper.Map<TaskNotificationViewModel>(entity);
                return new Response<TaskNotificationViewModel>(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Id: {@id}", id);
                return Helper.CreateExceptionResponse<TaskNotificationViewModel>(ex);
            }
        }

        public async Task<Response<Pagination<TaskNotificationViewModel>>> GetPage(TaskNotificationQueryModel query)
        {
            try
            {
                var predicate = BuildQuery(query);

                var queryResult = _dbContext.sm_TaskNotification
                    .AsNoTracking()
                    .Include(x => x.Task)
                    .Include(x => x.idm_User)
                    .Include(x => x.CreatedByUser)
                    .Where(predicate);
                var data = await queryResult.GetPageAsync(query);

                var result = _mapper.Map<Pagination<TaskNotificationViewModel>>(data);

                return Helper.CreateSuccessResponse(result);
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: Query: {@Param}", query);
                return Helper.CreateExceptionResponse<Pagination<TaskNotificationViewModel>>(ex);
            }
        }
        private Expression<Func<sm_TaskNotification, bool>> BuildQuery(TaskNotificationQueryModel query)
        {
            var currentUser = Helper.GetRequestInfo(_httpContextAccessor);

            var predicate = PredicateBuilder.New<sm_TaskNotification>(true);
            //if (!string.IsNullOrEmpty(query.FullTextSearch))
            //    predicate.And(s => s.Code.ToLower().Contains(query.FullTextSearch.ToLower()) || s.Name.ToLower().Contains(query.FullTextSearch.ToLower()));

            if (query.userId != Guid.Empty)
                predicate.And(s => s.UserId == query.userId);

            return predicate;
        }
        public async Task<Response> MarkAsRead(Guid notificationId)
        {
            try
            {
                var entity = await _dbContext.sm_TaskNotification
                    .FirstOrDefaultAsync(x => x.Id == notificationId);

                if (entity == null)
                    return Helper.CreateNotFoundResponse<bool>("Thông báo không tòn tại.");

                if (!entity.IsRead)
                {
                    entity.IsRead = true;
                    _dbContext.sm_TaskNotification.Update(entity);
                    await _dbContext.SaveChangesAsync();
                }

                return Helper.CreateSuccessResponse();
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: NotificationId: {@notificationId}", notificationId);
                return Helper.CreateExceptionResponse<bool>(ex);
            }
        }

        public async Task<Response> DeleteAllByUserId(Guid userId)
        {
            try
            {
                var notifications = await _dbContext.sm_TaskNotification
                    .Where(x => x.UserId == userId)
                    .ToListAsync();

                if (notifications == null || notifications.Count == 0)
                    return Helper.CreateNotFoundResponse<bool>("Không tìm thấy thông báo nào để xóa.");

                _dbContext.sm_TaskNotification.RemoveRange(notifications);
                await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse("Đã xóa tất cả thông báo thành công");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: UserId: {@userId}", userId);
                return Helper.CreateExceptionResponse<bool>(ex);
            }
        }
        public async Task<Response> MarkAllAsReadByUserId(Guid userId)
        {
            try
            {
                var notifications = await _dbContext.sm_TaskNotification
                    .Where(x => x.UserId == userId && !x.IsRead)
                    .ToListAsync();

                if (notifications == null || notifications.Count == 0)
                    return Helper.CreateNotFoundResponse<bool>("Không có thông báo chưa đọc nào để cập nhật.");

                foreach (var notification in notifications)
                {
                    notification.IsRead = true;
                }

                _dbContext.sm_TaskNotification.UpdateRange(notifications);
                await _dbContext.SaveChangesAsync();

                return Helper.CreateSuccessResponse("Đã đánh dấu tất cả thông báo là đã đọc.");
            }
            catch (Exception ex)
            {
                Log.Error(ex, string.Empty);
                Log.Information("Params: UserId: {@userId}", userId);
                return Helper.CreateExceptionResponse<bool>(ex);
            }
        }

        public async Task<Response<int>> CountUnread(Guid userId)
        {
            var count = await _dbContext.sm_TaskNotification
                .Where(x => x.UserId == userId && !x.IsRead)
                .CountAsync();

            return Helper.CreateSuccessResponse(count);
        }

    }

}
