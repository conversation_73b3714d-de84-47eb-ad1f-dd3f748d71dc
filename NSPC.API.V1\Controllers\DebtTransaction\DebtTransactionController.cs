﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using NSPC.Business.Services.DebtTransaction;
using NSPC.Business.Services.StockTransaction;
using NSPC.Common;

namespace NSPC.API.V1.Controllers.DebtTransaction
{
    // <summary>
    /// 
    /// </summary>
    [ApiVersion("1.0")]
    [ApiController]
    [Authorize, Route("api/v{api-version:apiVersion}/debt-transactions")]
    [ApiExplorerSettings(GroupName = "Tổng hợp công nợ")]
    public class DebtTransactionController : ControllerBase
    {
        private readonly IDebtTransactionHandler _handler;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="handler"></param>
        public DebtTransactionController(IDebtTransactionHandler handler)
        {
            _handler = handler;
        }

        /// <summary>
        /// Tạo bản ghi công nợ
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(Response<DebtTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> Create([FromBody] DebtTransactionCreateModel model)
        {
            var currentUser = Helper.GetRequestInfo(Request);
            var result = await _handler.Create(model, currentUser);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách công nợ
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("")]
        [ProducesResponseType(typeof(ResponsePagination<DebtTransactionViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilter([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<DebtTransactionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPage(filterObject);
            return Helper.TransformData(result);
        }

        /// <summary>
        /// Lấy danh sách báo cáo công nợ các đối tác (Khách hàng hoặc Nhà cung cấp)
        /// </summary>
        /// <param name="size"></param>
        /// <param name="page"></param>
        /// <param name="filter"></param>
        /// <param name="sort"></param>
        /// <returns></returns>
        [HttpGet, Route("report")]
        [ProducesResponseType(typeof(ResponsePagination<DebtTransactionReportViewModel>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetFilterReport([FromQuery] int size = 20, [FromQuery] int page = 1, [FromQuery] string filter = "{}", [FromQuery] string sort = "")
        {
            var filterObject = JsonConvert.DeserializeObject<DebtTransactionQueryModel>(filter);

            sort = string.IsNullOrWhiteSpace(sort) ? "-CreatedOnDate" : sort + "," + "-CreatedOnDate";
            filterObject.Sort = sort != null ? sort : filterObject.Sort;
            filterObject.Size = size;
            filterObject.Page = page;

            var result = await _handler.GetPageDebtReportCustomer(filterObject);
            return Helper.TransformData(result);
        }
    }
}
