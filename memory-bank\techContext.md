# Tech Context

_This document builds upon `projectbrief.md` and informs `systemPatterns.md` and `activeContext.md`._

## 1. Core Technologies

*   **Programming Languages:** [e.g., Python 3.9, Java 11, TypeScript 4.5]
*   **Frameworks/Libraries (Backend):** [e.g., Spring Boot 2.7, Django 4.0, Node.js with Express.js]
*   **Frameworks/Libraries (Frontend):** [e.g., React 18, Angular 14, Vue 3]
*   **Databases:** [e.g., PostgreSQL 14, MongoDB 5.0, MySQL 8.0]
*   **Caching Solutions:** [e.g., Redis, Memcached]
*   **Messaging Systems:** [e.g., Kafka, RabbitMQ]

## 2. Development Environment & Tools

*   **IDE(s):** [e.g., VS Code, IntelliJ IDEA, PyCharm]
*   **Version Control:** [e.g., Git, GitHub, GitLab, Bitbucket]
    *   **Branching Strategy:** [e.g., Git<PERSON>low, GitHub Flow. Link to detailed strategy if available.]
*   **Build Tools:** [e.g., Maven, Gradle, Webpack, npm/yarn scripts]
*   **Containerization (if applicable):** [e.g., Docker, Docker Compose]
*   **Key Dev Dependencies:** [Any other critical libraries or tools for local development, e.g., linters, formatters, testing frameworks not covered elsewhere.]

## 3. Testing Strategy

*   **Unit Testing Framework(s):** [e.g., JUnit, pytest, Jest, Mocha]
*   **Integration Testing Approach/Tools:**
*   **End-to-End (E2E) Testing Tools:** [e.g., Cypress, Selenium, Playwright]
*   **Code Coverage Goals:** [e.g., >80% for critical modules]

## 4. Deployment & Operations (DevOps)

*   **Hosting Environment(s):** [e.g., AWS, Azure, GCP, On-premise]
*   **CI/CD Pipeline Tools:** [e.g., Jenkins, GitLab CI, GitHub Actions, Azure DevOps]
*   **Infrastructure as Code (IaC) (if applicable):** [e.g., Terraform, CloudFormation, Ansible]
*   **Monitoring & Logging Tools:** [e.g., Prometheus, Grafana, ELK Stack, Datadog, Sentry]
*   **Deployment Strategy:** [e.g., Blue/Green, Canary, Rolling Updates]

## 5. Technical Constraints

*   [List any specific technical limitations or requirements from `projectbrief.md` or discovered since.]
    *   e.g., Must use specific SDKs, compliance with certain standards (HIPAA, GDPR), performance targets.

## 6. Key Third-Party Services & APIs

*   [List critical external services or APIs the project depends on, not covered in system integrations.]
    *   **Service 1:** [Name, Purpose, Link to Docs]
    *   **Service 2:** [Name, Purpose, Link to Docs]

## 7. Code Style and Conventions

*   **Coding Standards Document:** [Link to it if one exists, otherwise briefly outline key conventions.]
*   **Linters/Formatters in use:** [e.g., ESLint, Prettier, Black, Checkstyle]

_This document details the technologies used, development setup, technical constraints, and key dependencies of the project._ 